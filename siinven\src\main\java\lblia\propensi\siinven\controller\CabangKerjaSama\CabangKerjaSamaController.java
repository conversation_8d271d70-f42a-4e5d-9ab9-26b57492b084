package lblia.propensi.siinven.controller.CabangKerjaSama;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.*;

import jakarta.persistence.EntityNotFoundException;
import jakarta.validation.Valid;
import lblia.propensi.siinven.dto.request.CabangKerjaSama.CabangKerjaSamaRequestDTO;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.CabangKerjaSama.CabangKerjaSamaResponseDTO;
import lblia.propensi.siinven.security.jwt.JwtUtils;
import lblia.propensi.siinven.service.CabangKerjaSama.CabangKerjaSamaService;

@RestController
@RequestMapping("/api/cabang-kerjasama")
public class CabangKerjaSamaController {
    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    CabangKerjaSamaService cabangKerjaSamaService;

    private boolean isInvalidToken(String token) {
        return token == null || !token.startsWith("Bearer ");
    }

    @PostMapping("/create")
    public ResponseEntity<?> createCabangKerjaSama(@RequestHeader("Authorization") String token, @Valid @RequestBody CabangKerjaSamaRequestDTO cabangKerjaSamaDTO, BindingResult bindingResult) {
        var baseResponseDTO = new BaseResponseDTO<>();
        
        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }
        
        try {
            
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
                baseResponseDTO.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
            }

            if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Admin")) {
                baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
                baseResponseDTO.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
            }
            
            if (bindingResult.hasFieldErrors()) {
                String errorMessages = bindingResult.getFieldErrors().stream()
                        .map(FieldError::getDefaultMessage)
                        .collect(Collectors.joining("; "));

                baseResponseDTO.setStatus(HttpStatus.BAD_REQUEST.value());
                baseResponseDTO.setMessage(errorMessages);
                return new ResponseEntity<>(baseResponseDTO, HttpStatus.BAD_REQUEST);
            }

            // Check if cabang kerja sama already exists.
            if (cabangKerjaSamaService.findCabangKerjaSamaByNomorCabang(cabangKerjaSamaDTO.getNomorCabang()) != null) {
                baseResponseDTO.setStatus(HttpStatus.CONFLICT.value());
                baseResponseDTO.setMessage(String.format("Cabang kerja sama dengan nomor cabang %s sudah ada.", cabangKerjaSamaDTO.getNomorCabang()));
                return ResponseEntity.status(HttpStatus.CONFLICT).body(baseResponseDTO);
            }

            // Check if nama mitra in cabang kerja sama already exists.
            if (cabangKerjaSamaService.findCabangKerjaSamaByNamaMitra(cabangKerjaSamaDTO.getNamaMitra()) != null) {
                baseResponseDTO.setStatus(HttpStatus.CONFLICT.value());
                baseResponseDTO.setMessage(String.format("Cabang kerja sama dengan nama mitra %s sudah ada.", cabangKerjaSamaDTO.getNamaMitra()));
                return ResponseEntity.status(HttpStatus.CONFLICT).body(baseResponseDTO);

            }

            // Try creating new cabang kerja sama.
            cabangKerjaSamaService.createCabangKerjaSama(cabangKerjaSamaDTO);

            baseResponseDTO.setStatus(HttpStatus.CREATED.value());
            baseResponseDTO.setMessage("Success");
            baseResponseDTO.setData(String.format("Cabang kerja sama berhasil dibuat."));

            return new ResponseEntity<>(baseResponseDTO, HttpStatus.CREATED);

        } catch (EntityNotFoundException e) {
            baseResponseDTO.setStatus(HttpStatus.NOT_FOUND.value());
            baseResponseDTO.setMessage(e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.NOT_FOUND);

        } catch (IllegalArgumentException e) {
            baseResponseDTO.setStatus(HttpStatus.BAD_REQUEST.value());
            baseResponseDTO.setMessage(e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.BAD_REQUEST);

        } catch (IllegalStateException e) {
            baseResponseDTO.setStatus(HttpStatus.CONFLICT.value());
            baseResponseDTO.setMessage(e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.CONFLICT);
        
        } catch (Exception e) {
            baseResponseDTO.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            baseResponseDTO.setMessage("Terjadi error pada server. Error: " + e.getMessage());
            return new ResponseEntity<>(baseResponseDTO, HttpStatus.INTERNAL_SERVER_ERROR);
        }

    }

    @GetMapping("/{nomorCabang}")
    public ResponseEntity<?> detailCabangKerjaSama(@RequestHeader("Authorization") String token, @PathVariable("nomorCabang") String nomorCabang) {
        var baseResponseDTO = new BaseResponseDTO<>();
        
        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }
        
        try {
            
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
                baseResponseDTO.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
            }
    
            if (!(jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Gudang Pelaksana Umum") || 
                    jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Inventarisasi") || 
                    jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Pengadaan dan Pembelian") ||
                    jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Departemen SDM dan Umum") ||
                    jwtUtils.getRolesFromJWT(token.substring(7)).equals("Direktur Utama") ||
                    jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf keuangan") ||
                    jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Operasional Cabang") ||
                    jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Cabang") ||
                    jwtUtils.getRolesFromJWT(token.substring(7)).equals("Admin"))) {
                baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
                baseResponseDTO.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
            }
            
            var cks = cabangKerjaSamaService.getCabangKerjaSamaByNomorCabang(nomorCabang);
    
            baseResponseDTO.setStatus(HttpStatus.OK.value());
            baseResponseDTO.setData(cks);
            baseResponseDTO.setMessage("Success");
            return ResponseEntity.status(HttpStatus.OK).body(baseResponseDTO);
    
        } catch (EntityNotFoundException e) {
            baseResponseDTO.setStatus(HttpStatus.NOT_FOUND.value());
            baseResponseDTO.setMessage(e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(baseResponseDTO);
    
        } catch (Exception e) {
            baseResponseDTO.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            baseResponseDTO.setMessage("Terjadi error pada server. Error: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(baseResponseDTO);
        }
    }    

    @GetMapping("/all")
    public ResponseEntity<?> listCabangKerjaSama(@RequestHeader("Authorization") String token) {
        var baseResponseDTO = new BaseResponseDTO<>();
        
        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }
        
        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        if (!(jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Gudang Pelaksana Umum") || 
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Inventarisasi") || 
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Pengadaan dan Pembelian") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Departemen SDM dan Umum") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Direktur Utama") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf keuangan") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Admin"))) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }
                
        List<CabangKerjaSamaResponseDTO> listCKS = cabangKerjaSamaService.getListCabangKerja();
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(listCKS);
        baseResponseDTO.setMessage("Success");
        return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
    }

    @PutMapping("/update/{nomorCabang}")
    public ResponseEntity<?> updateCabangKerjaSama(
            @RequestHeader("Authorization") String token,
            @PathVariable("nomorCabang") String nomorCabang, 
            @Valid @RequestBody CabangKerjaSamaRequestDTO cabangKerjaSamaDTO, 
            BindingResult bindingResult) {
        
        var baseResponseDTO = new BaseResponseDTO<>();
        
        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }
        
        try {
            
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
                baseResponseDTO.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
            }

            if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Admin")) {
                baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
                baseResponseDTO.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
            }
            
            if (bindingResult.hasFieldErrors()) {
                String errorMessages = bindingResult.getFieldErrors().stream()
                        .map(FieldError::getDefaultMessage)
                        .collect(Collectors.joining("; "));
    
                baseResponseDTO.setStatus(HttpStatus.BAD_REQUEST.value());
                baseResponseDTO.setMessage(errorMessages);
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(baseResponseDTO);
            }

            // Ensure the path variable matches the request body
            if (!nomorCabang.equals(cabangKerjaSamaDTO.getNomorCabang())) {
                baseResponseDTO.setStatus(HttpStatus.BAD_REQUEST.value());
                baseResponseDTO.setMessage("Nomor cabang dalam path dan request body tidak cocok.");
                return ResponseEntity.badRequest().body(baseResponseDTO);
            }
    
            // Check if cabang kerja sama exists.
            if (cabangKerjaSamaService.findCabangKerjaSamaByNomorCabang(cabangKerjaSamaDTO.getNomorCabang()) == null) {
                baseResponseDTO.setStatus(HttpStatus.NOT_FOUND.value());
                baseResponseDTO.setMessage(String.format("Cabang kerja sama dengan nomor cabang %s tidak ditemukan.", cabangKerjaSamaDTO.getNomorCabang()));
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(baseResponseDTO);
            }

            // Check if nama mitra in cabang kerja sama already exists.
            var checkNamaMitra = cabangKerjaSamaService.findCabangKerjaSamaByNamaMitra(cabangKerjaSamaDTO.getNamaMitra());
            if (checkNamaMitra != null && !checkNamaMitra.getNomorCabang().equals(nomorCabang)) {
                baseResponseDTO.setStatus(HttpStatus.CONFLICT.value());
                baseResponseDTO.setMessage(String.format("Cabang kerja sama dengan nama mitra %s sudah ada.", cabangKerjaSamaDTO.getNamaMitra()));
                return ResponseEntity.status(HttpStatus.CONFLICT).body(baseResponseDTO);
            }

            // Try updating cabang kerja sama
            cabangKerjaSamaService.updateCabangKerjaSama(cabangKerjaSamaDTO);
    
            baseResponseDTO.setStatus(HttpStatus.OK.value());
            baseResponseDTO.setMessage("Success");
            baseResponseDTO.setData("Cabang kerja sama berhasil diperbarui.");
            return ResponseEntity.status(HttpStatus.OK).body(baseResponseDTO);
    
        } catch (EntityNotFoundException e) {
            baseResponseDTO.setStatus(HttpStatus.NOT_FOUND.value());
            baseResponseDTO.setMessage(e.getMessage());
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(baseResponseDTO);
    
        } catch (IllegalStateException e) {
            baseResponseDTO.setStatus(HttpStatus.CONFLICT.value());
            baseResponseDTO.setMessage(e.getMessage());
            return ResponseEntity.status(HttpStatus.CONFLICT).body(baseResponseDTO);
    
        } catch (IllegalArgumentException e) {
            baseResponseDTO.setStatus(HttpStatus.BAD_REQUEST.value());
            baseResponseDTO.setMessage(e.getMessage());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(baseResponseDTO);
    
        } catch (Exception e) {
            baseResponseDTO.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            baseResponseDTO.setMessage("Terjadi error pada server. Error: " + e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(baseResponseDTO);
        }
    }

    @DeleteMapping("/delete/{nomorCabang}")
    public ResponseEntity<?> deleteCabangKerjaSama(@RequestHeader("Authorization") String token, @PathVariable("nomorCabang") String nomorCabang) {

        var baseResponseDTO = new BaseResponseDTO<>();
        
        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }
            
        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Admin")) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }
        
        // Check if cabang kerja sama exists.
        var cks = cabangKerjaSamaService.findCabangKerjaSamaByNomorCabang(nomorCabang);
        if (cks == null) {
            // Cabang kerja sama does not exists.
            baseResponseDTO.setStatus(HttpStatus.NOT_FOUND.value());
            baseResponseDTO.setMessage(String.format("Cabang kerja sama dengan nomor cabang %s tidak ditemukan.", nomorCabang));
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(baseResponseDTO);
        }

        // Delete existing cabang kerja sama.
        cabangKerjaSamaService.deleteCabangKerjaSama(cks);
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(String.format("Cabang kerja sama berhasil dihapus."));
        baseResponseDTO.setMessage("Success");
        return ResponseEntity.status(HttpStatus.OK).body(baseResponseDTO);
    }
}
