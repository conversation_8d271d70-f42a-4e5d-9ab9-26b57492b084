package lblia.propensi.siinven.repository;

import java.util.List;
import java.util.Optional;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import jakarta.transaction.Transactional;
import lblia.propensi.siinven.model.StokBarang;

@Repository
public interface StokBarangRepository extends JpaRepository<StokBarang, Integer> {
    List<StokBarang> findByKategoriBarang(String kategoriBarang);
    List<StokBarang> findByNomorCabang(String nomorCabang);

    @Query("SELECT s FROM StokBarang s WHERE s.nomorCabang != '001'")
    List<StokBarang> findAllExceptMainBranch();

      
  
    
   
    @Modifying
    @Transactional
    @Query("DELETE FROM StokBarang s WHERE s.kodeBarang = :kodeBarang AND s.nomorCabang = :nomorCabang")
    void deleteByKodeBarangAndNomorCabang(@Param("kodeBarang") Integer kodeBarang, @Param("nomorCabang") String nomorCabang);

    boolean existsByKodeBarangAndNomorCabang(Integer kodeBarang, String nomorCabang);

    @Query("SELECT s FROM StokBarang s WHERE s.kodeBarang = :kodeBarang AND s.nomorCabang = :nomorCabang")
    Optional<StokBarang> findByKodeBarangAndNomorCabang(Integer kodeBarang, String nomorCabang);


    //Fitur barang dengan stok menipis
    @Query("SELECT s FROM StokBarang s WHERE s.stokBarang <= :threshold AND s.nomorCabang = '001'")
    List<StokBarang> findByStokBarangLessThanEqual(@Param("threshold") Integer threshold);
    
    @Query("SELECT s FROM StokBarang s WHERE s.stokBarang BETWEEN :min AND :max AND s.nomorCabang = '001'")
    List<StokBarang> findByStokBarangBetween(
        @Param("min") Integer min, 
        @Param("max") Integer max);  
}
