package lblia.propensi.siinven.service.CabangKerjaSama;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import jakarta.persistence.EntityNotFoundException;
import lblia.propensi.siinven.dto.request.CabangKerjaSama.CabangKerjaSamaRequestDTO;
import lblia.propensi.siinven.dto.response.CabangKerjaSama.CabangKerjaSamaResponseDTO;
import lblia.propensi.siinven.model.CabangAsli;
import lblia.propensi.siinven.model.CabangKerjaSama;
import lblia.propensi.siinven.model.Pengguna;
import lblia.propensi.siinven.repository.PenggunaDb;
import lblia.propensi.siinven.repository.CabangAsliDb;
import lblia.propensi.siinven.repository.CabangKerjaSamaDb;
import lblia.propensi.siinven.service.UserRestService;

@Service
public class CabangKerjaSamaServiceImpl implements CabangKerjaSamaService {
    @Autowired
    UserRestService userRestService;

    @Autowired
    PenggunaDb penggunaDb;

    @Autowired
    CabangKerjaSamaDb cabangKerjaSamaDb;

    @Autowired
    CabangAsliDb cabangAsliDb;

    @Override
    public CabangKerjaSama findCabangKerjaSamaByNomorCabang(String nomorCabang) {
        return cabangKerjaSamaDb.findByNomorCabang(nomorCabang);
    }

    @Override
    public CabangKerjaSama findCabangKerjaSamaByNamaMitra(String namaMitra) {
        return cabangKerjaSamaDb.findByNamaMitra(namaMitra);
    }

    @Override
    public void createCabangKerjaSama (CabangKerjaSamaRequestDTO cabangKerjaSamaDTO) {
        // Retrieve Nomor Cabang
        String nomorCabangFromDTO = cabangKerjaSamaDTO.getNomorCabang();

        // Check if the nomorCabang already exists in CabangAsli
        if (cabangAsliDb.findByNomorCabang(nomorCabangFromDTO) != null) {
            throw new IllegalStateException(
                String.format("Nomor cabang %s sudah digunakan oleh cabang asli. Silakan gunakan nomor cabang yang berbeda.", nomorCabangFromDTO)
            );
        }
        
        // Check if the Kepala Operasional exists
        Pengguna kepalaOperasional = userRestService.getUserByIdPengguna(cabangKerjaSamaDTO.getIdKaryawanKepalaOperasional());
        if (kepalaOperasional == null) {
            throw new EntityNotFoundException("Pengguna dengan ID " + cabangKerjaSamaDTO.getIdKaryawanKepalaOperasional() + " tidak ditemukan.");
        }
        Pengguna kepalaCabang = userRestService.getUserByIdPengguna(cabangKerjaSamaDTO.getIdKaryawanKepalaCabang());
        if (kepalaCabang == null) {
            throw new EntityNotFoundException("Pengguna dengan ID " + cabangKerjaSamaDTO.getIdKaryawanKepalaCabang() + " tidak ditemukan.");
        }

        // Check if the Kepala Operasional is already assigned to another Cabang
        CabangAsli existingCA = cabangAsliDb.findByIdKepalaOperasional(kepalaOperasional.getIdKaryawan());
        CabangKerjaSama existingCKS = cabangKerjaSamaDb.findByKepalaOperasionalCabang(kepalaOperasional);
        if (existingCKS != null && !existingCKS.getNomorCabang().equals(nomorCabangFromDTO)) {
            throw new IllegalStateException(
                String.format("Kepala Operasional sudah terdaftar di cabang kerja sama lain dengan nomor %s.", existingCKS.getNomorCabang())
            );
        } else if (existingCA != null && !existingCA.getNomorCabang().equals(nomorCabangFromDTO)) {
            throw new IllegalStateException(
                String.format("Kepala Operasional sudah terdaftar di cabang asli dengan nomor %s.", existingCA.getNomorCabang())
            );
        }

        // Check if the Kepala Cabang is already assigned to another Cabang
        CabangAsli existingKacabCA = cabangAsliDb.findByIdKepalaCabang(kepalaCabang.getIdKaryawan());
        CabangKerjaSama existingKacabCKS = cabangKerjaSamaDb.findByIdKepalaCabang(kepalaCabang.getIdKaryawan());
        if (existingKacabCKS != null && !existingKacabCKS.getNomorCabang().equals(nomorCabangFromDTO)) {
            throw new IllegalStateException(
                String.format("Kepala Cabang sudah terdaftar di cabang kerja sama lain dengan nomor %s.", existingKacabCKS.getNomorCabang())
            );
        } else if (existingKacabCA != null && !existingKacabCA.getNomorCabang().equals(nomorCabangFromDTO)) {
            throw new IllegalStateException(
                String.format("Kepala Cabang sudah terdaftar di cabang asli dengan nomor %s.", existingKacabCA.getNomorCabang())
            );
        }

        CabangKerjaSama cabangKerjaSama = new CabangKerjaSama();
        cabangKerjaSama.setNomorCabang(nomorCabangFromDTO);
        cabangKerjaSama.setNamaMitra(cabangKerjaSamaDTO.getNamaMitra());
        cabangKerjaSama.setAlamat(cabangKerjaSamaDTO.getAlamat());
        cabangKerjaSama.setKontak(cabangKerjaSamaDTO.getKontak());
        cabangKerjaSama.setJumlahKaryawan(cabangKerjaSamaDTO.getJumlahKaryawan());
        cabangKerjaSama.setJamOperasional(cabangKerjaSamaDTO.getJamOperasional());
        cabangKerjaSama.setMasaBerlakuKontrak(cabangKerjaSamaDTO.getMasaBerlakuKontrak());
        cabangKerjaSama.setKepalaOperasionalCabang(kepalaOperasional);
        cabangKerjaSama.setIdKepalaCabang(kepalaCabang.getIdKaryawan());

        // Ensure daftarKaryawan is initialized
        if (cabangKerjaSama.getDaftarKaryawan() == null) {
            cabangKerjaSama.setDaftarKaryawan(new ArrayList<>());
        }

        // Ensure Kepala Operasional is added to daftarKaryawan
        if (!cabangKerjaSama.getDaftarKaryawan().contains(kepalaOperasional)) {
            cabangKerjaSama.getDaftarKaryawan().add(kepalaOperasional);
        }

        // Ensure Kepala Cabang is added to daftarKaryawan
        if (!cabangKerjaSama.getDaftarKaryawan().contains(kepalaCabang)) {
            cabangKerjaSama.getDaftarKaryawan().add(kepalaCabang);
        }

        cabangKerjaSamaDb.save(cabangKerjaSama);

        // Set nomorCabang for Kepala Operasional Cabang
        kepalaOperasional.setNomorCabang(cabangKerjaSama.getNomorCabang());
        kepalaCabang.setNomorCabang(cabangKerjaSama.getNomorCabang());

        // Assign cabang kerja sama to Kepala Operasional
        kepalaOperasional.setCabangKerjaSama(cabangKerjaSama);
        kepalaCabang.setCabangKerjaSama(cabangKerjaSama);

        penggunaDb.save(kepalaOperasional);
        penggunaDb.save(kepalaCabang);
    }

    @Override
    public CabangKerjaSamaResponseDTO getCabangKerjaSamaByNomorCabang(String nomorCabang) {
        var cabangKerjaSama = findCabangKerjaSamaByNomorCabang(nomorCabang);

        if (cabangKerjaSama == null) {
            throw new EntityNotFoundException(
                String.format("Cabang kerja sama dengan nomor cabang %s tidak ditemukan", nomorCabang)
            );
        }
        
        return cabangKerjaSamaTOCabangKerjaSamaResponseDTO(cabangKerjaSama);
    }

    @Override
    public List<CabangKerjaSamaResponseDTO> getListCabangKerja() {
        List<CabangKerjaSama> listCabangKerjaSama = cabangKerjaSamaDb.findAll();
        List<CabangKerjaSamaResponseDTO> listCabangKerjaSamaDTO = new ArrayList<>();
        for (CabangKerjaSama cks : listCabangKerjaSama) {
            listCabangKerjaSamaDTO.add(cabangKerjaSamaTOCabangKerjaSamaResponseDTO(cks));
        }
        return listCabangKerjaSamaDTO;
    }

    @Override 
    public void updateCabangKerjaSama(CabangKerjaSamaRequestDTO cabangKerjaSamaDTO) throws Exception {
        // Retrieve Nomor Cabang
        String nomorCabangFromDTO = cabangKerjaSamaDTO.getNomorCabang();

        // Check if the Kepala Operasional exists
        Pengguna kepalaOperasional = userRestService.getUserByIdPengguna(cabangKerjaSamaDTO.getIdKaryawanKepalaOperasional());
        if (kepalaOperasional == null) {
            throw new EntityNotFoundException("Pengguna dengan ID " + cabangKerjaSamaDTO.getIdKaryawanKepalaOperasional() + " tidak ditemukan.");
        }
        Pengguna kepalaCabang = userRestService.getUserByIdPengguna(cabangKerjaSamaDTO.getIdKaryawanKepalaCabang());
        if (kepalaCabang == null) {
            throw new EntityNotFoundException("Pengguna dengan ID " + cabangKerjaSamaDTO.getIdKaryawanKepalaCabang() + " tidak ditemukan.");
        }

        // Check if the Kepala Operasional is already assigned to another Cabang
        CabangAsli existingCA = cabangAsliDb.findByIdKepalaOperasional(kepalaOperasional.getIdKaryawan());
        CabangKerjaSama existingCKS = cabangKerjaSamaDb.findByKepalaOperasionalCabang(kepalaOperasional);
        if (existingCKS != null && !existingCKS.getNomorCabang().equals(nomorCabangFromDTO)) {
            throw new IllegalStateException(
                String.format("Kepala Operasional sudah terdaftar di cabang kerja sama lain dengan nomor %s.", existingCKS.getNomorCabang())
            );
        } else if (existingCA != null && !existingCA.getNomorCabang().equals(nomorCabangFromDTO)) {
            throw new IllegalStateException(
                String.format("Kepala Operasional sudah terdaftar di cabang asli dengan nomor %s.", existingCA.getNomorCabang())
            );
        }

        // Check if the Kepala Cabang is already assigned to another Cabang
        CabangAsli existingKacabCA = cabangAsliDb.findByIdKepalaCabang(kepalaCabang.getIdKaryawan());
        CabangKerjaSama existingKacabCKS = cabangKerjaSamaDb.findByIdKepalaCabang(kepalaCabang.getIdKaryawan());
        if (existingKacabCKS != null && !existingKacabCKS.getNomorCabang().equals(nomorCabangFromDTO)) {
            throw new IllegalStateException(
                String.format("Kepala Cabang sudah terdaftar di cabang kerja sama lain dengan nomor %s.", existingKacabCKS.getNomorCabang())
            );
        } else if (existingKacabCA != null && !existingKacabCA.getNomorCabang().equals(nomorCabangFromDTO)) {
            throw new IllegalStateException(
                String.format("Kepala Cabang sudah terdaftar di cabang asli dengan nomor %s.", existingKacabCA.getNomorCabang())
            );
        }
        
        CabangKerjaSama getCKS = findCabangKerjaSamaByNomorCabang(nomorCabangFromDTO);
        
        if (getCKS == null) {
            throw new EntityNotFoundException(
                String.format("Cabang kerja sama dengan nomor cabang %s tidak ditemukan", nomorCabangFromDTO)
            );
        }

        Pengguna mantanKepalaOperasional = getCKS.getKepalaOperasionalCabang();
        Pengguna mantanKepalaCabang = userRestService.getUserByIdPengguna(getCKS.getIdKepalaCabang());

        // Ensure Mantan Kepala Operasional is added to daftarKaryawan
        if (!getCKS.getDaftarKaryawan().contains(mantanKepalaOperasional)) {
            getCKS.getDaftarKaryawan().add(mantanKepalaOperasional);
        }
        // Ensure Mantan Kepala Cabang is added to daftarKaryawan
        if (!getCKS.getDaftarKaryawan().contains(mantanKepalaCabang)) {
            getCKS.getDaftarKaryawan().add(mantanKepalaCabang);
        }

        getCKS.setNamaMitra(cabangKerjaSamaDTO.getNamaMitra());
        getCKS.setAlamat(cabangKerjaSamaDTO.getAlamat());
        getCKS.setKontak(cabangKerjaSamaDTO.getKontak());
        getCKS.setJumlahKaryawan(cabangKerjaSamaDTO.getJumlahKaryawan());
        getCKS.setJamOperasional(cabangKerjaSamaDTO.getJamOperasional());
        getCKS.setMasaBerlakuKontrak(cabangKerjaSamaDTO.getMasaBerlakuKontrak());
        getCKS.setKepalaOperasionalCabang(kepalaOperasional);
        getCKS.setIdKepalaCabang(cabangKerjaSamaDTO.getIdKaryawanKepalaCabang());

        // Ensure Kepala Operasional is added to daftarKaryawan
        if (!getCKS.getDaftarKaryawan().contains(kepalaOperasional)) {
            getCKS.getDaftarKaryawan().add(kepalaOperasional);
        }

        // Ensure Kepala Cabang is added to daftarKaryawan
        if (!getCKS.getDaftarKaryawan().contains(kepalaCabang)) {
            getCKS.getDaftarKaryawan().add(kepalaCabang);
        }

        // Save the updated entity
        cabangKerjaSamaDb.save(getCKS);

        // Set nomorCabang Kepala Operasional Cabang
        kepalaOperasional.setNomorCabang(getCKS.getNomorCabang());
        kepalaCabang.setNomorCabang(getCKS.getNomorCabang());


        // Assign cabang kerja sama to Kepala Operasional
        kepalaOperasional.setCabangKerjaSama(getCKS);
        kepalaCabang.setCabangKerjaSama(getCKS);

        penggunaDb.save(kepalaOperasional);
        penggunaDb.save(kepalaCabang);
    }

    @Override
    public void deleteCabangKerjaSama(CabangKerjaSama cabangKerjaSama) {
        // Hapus referensi ke CabangKerjaSama pada daftar karyawan
        for (Pengguna pengguna : cabangKerjaSama.getDaftarKaryawan()) {
            pengguna.setCabangKerjaSama(null);
            pengguna.setNomorCabang("001");
            penggunaDb.save(pengguna);
        }

        // Hapus referensi kepala operasional cabang jika ada
        if (cabangKerjaSama.getKepalaOperasionalCabang() != null) {
            cabangKerjaSama.getKepalaOperasionalCabang().setNomorCabang("001");
            cabangKerjaSama.setKepalaOperasionalCabang(null);
        }

        // Hapus referensi kepala cabang jika ada
        if (cabangKerjaSama.getIdKepalaCabang() != null) {
            Pengguna kepalaCabang = userRestService.getUserByIdPengguna(cabangKerjaSama.getIdKepalaCabang());
            kepalaCabang.setNomorCabang("001");
            cabangKerjaSama.setIdKepalaCabang(null);
        }

        List<Pengguna> allPengguna = penggunaDb.findAllByNomorCabang(cabangKerjaSama.getNomorCabang());
        for (Pengguna pengguna: allPengguna) {
            pengguna.setNomorCabang("001");
            penggunaDb.save(pengguna);
        }

        // Simpan perubahan sebelum menghapus cabang kerja sama
        cabangKerjaSamaDb.save(cabangKerjaSama);

        cabangKerjaSamaDb.delete(cabangKerjaSama);
    }

    private CabangKerjaSamaResponseDTO cabangKerjaSamaTOCabangKerjaSamaResponseDTO (CabangKerjaSama cabangKerjaSama) {
        var cabangKerjaSamaResponseDTO = new CabangKerjaSamaResponseDTO();
        cabangKerjaSamaResponseDTO.setNomorCabang(cabangKerjaSama.getNomorCabang());
        cabangKerjaSamaResponseDTO.setNamaMitra(cabangKerjaSama.getNamaMitra());
        cabangKerjaSamaResponseDTO.setAlamat(cabangKerjaSama.getAlamat());
        cabangKerjaSamaResponseDTO.setKontak(cabangKerjaSama.getKontak());
        cabangKerjaSamaResponseDTO.setJumlahKaryawan(cabangKerjaSama.getJumlahKaryawan());
        cabangKerjaSamaResponseDTO.setJamOperasional(cabangKerjaSama.getJamOperasional());
        cabangKerjaSamaResponseDTO.setMasaBerlakuKontrak(cabangKerjaSama.getMasaBerlakuKontrak());
        cabangKerjaSamaResponseDTO.setIdKaryawanKepalaOperasional(cabangKerjaSama.getKepalaOperasionalCabang().getIdKaryawan());
        cabangKerjaSamaResponseDTO.setUsernameKepalaOperasionalCabang(cabangKerjaSama.getKepalaOperasionalCabang().getUsername());
        cabangKerjaSamaResponseDTO.setIdKaryawanKepalaCabang(cabangKerjaSama.getIdKepalaCabang());
        Pengguna kepalaCabang = userRestService.getUserByIdPengguna(cabangKerjaSama.getIdKepalaCabang());
        cabangKerjaSamaResponseDTO.setUsernameKepalaCabang(kepalaCabang.getUsername());
        cabangKerjaSamaResponseDTO.setCreatedAt(cabangKerjaSama.getCreatedAt().toString());
        cabangKerjaSamaResponseDTO.setUpdatedAt(cabangKerjaSama.getUpdatedAt().toString());
        return cabangKerjaSamaResponseDTO;
    }
}
