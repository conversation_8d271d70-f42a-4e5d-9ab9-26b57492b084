package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.ApprovalRequestDTO;
import lblia.propensi.siinven.dto.request.KonfirmasiReturnRequestDTO;
import lblia.propensi.siinven.dto.request.ReturnRequestDTO;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.ReturnResponseDTO;
import lblia.propensi.siinven.model.Pengguna;
import lblia.propensi.siinven.model.Pengguna.RolePengguna;
import lblia.propensi.siinven.model.Return;
import lblia.propensi.siinven.model.StokBarang;
import lblia.propensi.siinven.model.StokCabang;
import lblia.propensi.siinven.repository.PenggunaDb;
import lblia.propensi.siinven.repository.ReturnDb;
import lblia.propensi.siinven.repository.StokBarangRepository;
import lblia.propensi.siinven.repository.StokCabangRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.server.ResponseStatusException;

import java.util.Date;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class ReturnServiceImpl implements ReturnService {
    
    @Autowired
    private ReturnDb returnRepository;

    @Autowired
    private StokBarangRepository stokBarangRepository;

    @Autowired
    private StokCabangRepository stokCabangRepository;

    @Autowired
    private PenggunaDb penggunaRepository;


    @Autowired
    private LogStokBarangService logStokBarangService;


    @Autowired
    private NotifikasiRestService notifikasiRestService;

    @Autowired
    private PenggunaDb penggunaDb;

    @Override
    @Transactional
    public BaseResponseDTO<ReturnResponseDTO> createReturn(ReturnRequestDTO returnRequestDTO, String idPengaju) {
        var response = new BaseResponseDTO<ReturnResponseDTO>();

        // Dapatkan data user pengaju
        Optional<Pengguna> pengajuOptional = penggunaRepository.findByUsername(idPengaju);
        if (pengajuOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,
                "User pengaju tidak ditemukan.");
        }

        Pengguna pengaju = pengajuOptional.get();
        String nomorCabang = pengaju.getNomorCabang();

        if (nomorCabang == null) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                "Nomor cabang user tidak ditemukan.");
        }

        // Dapatkan data stok barang dari cabang user
        Optional<StokBarang> stokBarangOptional = stokBarangRepository.findByKodeBarangAndNomorCabang(
            returnRequestDTO.getKodeBarang(), nomorCabang);
        if (stokBarangOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Barang dengan kode " + returnRequestDTO.getKodeBarang() + " tidak ditemukan di cabang " + nomorCabang + ".");
        }

        StokBarang stokBarang = stokBarangOptional.get();

        // Validasi stok mencukupi
        if (stokBarang.getStokBarang() < returnRequestDTO.getStokInput()) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                "Stok barang di cabang tidak mencukupi. Stok tersedia: " + stokBarang.getStokBarang());
        }
        
        // Buat objek Return
        Return returnObj = new Return();
        returnObj.setIdPengajuan(idPengaju);
        returnObj.setKodeBarang(returnRequestDTO.getKodeBarang());
        returnObj.setNamaBarang(stokBarang.getNamaBarang());
        returnObj.setStokBarangSaatIni(stokBarang.getStokBarang());
        returnObj.setStokInput(returnRequestDTO.getStokInput());
        returnObj.setPerlakuan(returnRequestDTO.getPerlakuan());
        returnObj.setHargaBarang(stokBarang.getHargaBarang());
        returnObj.setAlasanReturn(returnRequestDTO.getAlasanReturn());
        returnObj.setStatusApprovalKepalaCabang("MENUNGGU");
        returnObj.setStatusApproval("MENUNGGU");
        returnObj.setStatusRetur("PENGAJUAN");
        
        // Simpan Return ke database
        Return savedReturn = returnRepository.save(returnObj);

        // Dapatkan nomor cabang dari pengajuan (idPengaju adalah user yang membuat return)
        String nomorCabang = getNomorCabangFromIdPengajuan(idPengaju);
        // Jika tidak ditemukan, gunakan fallback ke cabang pusat
        if (nomorCabang == null) {
            nomorCabang = "001";
        }

        // Kirim notifikasi ke kepala departemen SDM dan staf inventaris
        String pesan = "Permintaan return barang " + returnObj.getNamaBarang() +
                       " sebanyak " + returnObj.getStokInput() +
                       " dengan alasan: " + returnObj.getAlasanReturn();

        // Kirim ke kepala departemen SDM
        notifikasiRestService.kirimNotifikasi(
            convertRoleFormat(RolePengguna.KEPALA_OPERASIONAL_CABANG.name()),
            convertRoleFormat(RolePengguna.KEPALA_DEPARTEMEN_SDM_DAN_UMUM.name()),
            nomorCabang, // Nomor cabang yang benar
            pesan,
            savedReturn.getIdInputStokBarangReturn()
        );

        // Kirim ke staf inventaris
        notifikasiRestService.kirimNotifikasi(
            convertRoleFormat(RolePengguna.KEPALA_OPERASIONAL_CABANG.name()),
            convertRoleFormat(RolePengguna.STAF_INVENTARISASI.name()),
            nomorCabang, // Nomor cabang yang benar
            pesan,
            savedReturn.getIdInputStokBarangReturn()
        );
        
        // Buat response
        response.setStatus(HttpStatus.CREATED.value());
        response.setMessage("Permintaan return berhasil dibuat.");
        response.setData(mapToDTO(savedReturn));
        response.setTimestamp(new Date());
        
        return response;
    }
    
    @Override
    public BaseResponseDTO<List<ReturnResponseDTO>> getAllReturns() {
        var response = new BaseResponseDTO<List<ReturnResponseDTO>>();
        
        List<Return> returns = returnRepository.findAll();
        List<ReturnResponseDTO> returnDTOs = returns.stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
        
        response.setStatus(HttpStatus.OK.value());
        response.setMessage("Data return berhasil diambil.");
        response.setData(returnDTOs);
        response.setTimestamp(new Date());
        
        return response;
    }
    
    @Override
    public BaseResponseDTO<List<ReturnResponseDTO>> getReturnsByStatus(String status) {
        var response = new BaseResponseDTO<List<ReturnResponseDTO>>();
        
        List<Return> returns;
        if (status.equalsIgnoreCase("approval")) {
            returns = returnRepository.findByStatusApproval("MENUNGGU");
        } else {
            returns = returnRepository.findByStatusRetur(status.toUpperCase());
        }
        
        List<ReturnResponseDTO> returnDTOs = returns.stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
        
        response.setStatus(HttpStatus.OK.value());
        response.setMessage("Data return dengan status " + status + " berhasil diambil.");
        response.setData(returnDTOs);
        response.setTimestamp(new Date());
        
        return response;
    }
    
    @Override
    public BaseResponseDTO<ReturnResponseDTO> getReturnById(String idReturn) {
        var response = new BaseResponseDTO<ReturnResponseDTO>();
        
        Optional<Return> returnOptional = returnRepository.findById(idReturn);
        if (returnOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, 
                "Return dengan ID " + idReturn + " tidak ditemukan.");
        }
        
        response.setStatus(HttpStatus.OK.value());
        response.setMessage("Data return berhasil diambil.");
        response.setData(mapToDTO(returnOptional.get()));
        response.setTimestamp(new Date());
        
        return response;
    }
    
    @Override
    @Transactional
    public BaseResponseDTO<ReturnResponseDTO> approveReturn(String idReturn, ApprovalRequestDTO approvalDTO, String idApprover) {
        var response = new BaseResponseDTO<ReturnResponseDTO>();

        Optional<Return> returnOptional = returnRepository.findById(idReturn);
        if (returnOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Return dengan ID " + idReturn + " tidak ditemukan.");
        }

        Return returnObj = returnOptional.get();

        // Dapatkan role approver
        Optional<Pengguna> approverOptional = penggunaRepository.findByUsername(idApprover);
        if (approverOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Approver tidak ditemukan.");
        }

        Pengguna approver = approverOptional.get();
        String approverRole = approver.getRole();

        // Logic approval bertingkat
        if ("Kepala Cabang".equals(approverRole)) {
            // Approval oleh Kepala Cabang (tahap pertama)
            if (!returnObj.getStatusApprovalKepalaCabang().equals("MENUNGGU")) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                    "Return ini sudah diproses oleh Kepala Cabang sebelumnya.");
            }

            returnObj.setStatusApprovalKepalaCabang(approvalDTO.getStatusApproval());

            if (approvalDTO.getStatusApproval().equals("DITOLAK")) {
                returnObj.setStatusRetur("DITOLAK");
                Return savedReturn = returnRepository.save(returnObj);

                // Kirim notifikasi penolakan
                kirimNotifikasiKeUserPembuat(
                    convertRoleFormat(RolePengguna.KEPALA_CABANG.name()),
                    returnObj.getIdPengajuan(),
                    "Pengajuan return untuk barang " + returnObj.getNamaBarang() + " telah DITOLAK oleh Kepala Cabang.",
                    savedReturn.getIdInputStokBarangReturn()
                );

                response.setStatus(HttpStatus.OK.value());
                response.setMessage("Pengajuan return berhasil ditolak oleh Kepala Cabang.");
                response.setData(mapToDTO(savedReturn));
                response.setTimestamp(new Date());
                return response;
            }

            // Jika disetujui Kepala Cabang, lanjut ke Kepala Departemen SDM
            Return savedReturn = returnRepository.save(returnObj);

            // Kirim notifikasi ke Kepala Departemen SDM
            kirimNotifikasiKeUserPembuat(
                convertRoleFormat(RolePengguna.KEPALA_CABANG.name()),
                returnObj.getIdPengajuan(),
                "Pengajuan return untuk barang " + returnObj.getNamaBarang() + " telah DISETUJUI oleh Kepala Cabang. Menunggu approval Kepala Departemen SDM.",
                savedReturn.getIdInputStokBarangReturn()
            );

            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Pengajuan return berhasil disetujui oleh Kepala Cabang. Menunggu approval Kepala Departemen SDM.");
            response.setData(mapToDTO(savedReturn));
            response.setTimestamp(new Date());
            return response;

        } else if ("Kepala Departemen SDM dan Umum".equals(approverRole)) {
            // Approval oleh Kepala Departemen SDM (tahap kedua)
            if (!returnObj.getStatusApprovalKepalaCabang().equals("DISETUJUI")) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                    "Return harus disetujui oleh Kepala Cabang terlebih dahulu.");
            }

            if (!returnObj.getStatusApproval().equals("MENUNGGU")) {
                throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                    "Return ini sudah diproses oleh Kepala Departemen SDM sebelumnya.");
            }

            // Update status approval
            returnObj.setStatusApproval(approvalDTO.getStatusApproval());

            // Jika ditolak oleh Kepala Departemen SDM
            if (approvalDTO.getStatusApproval().equals("DITOLAK")) {
                returnObj.setStatusRetur("DITOLAK");
                Return savedReturn = returnRepository.save(returnObj);

                // Kirim notifikasi bahwa pengajuan return ditolak ke user yang membuat pengajuan
                kirimNotifikasiKeUserPembuat(
                    convertRoleFormat(RolePengguna.KEPALA_DEPARTEMEN_SDM_DAN_UMUM.name()),
                    returnObj.getIdPengajuan(),
                    "Pengajuan return untuk barang " + returnObj.getNamaBarang() + " telah DITOLAK oleh Kepala Departemen SDM.",
                    savedReturn.getIdInputStokBarangReturn()
                );

                response.setStatus(HttpStatus.OK.value());
                response.setMessage("Pengajuan return berhasil ditolak oleh Kepala Departemen SDM.");
                response.setData(mapToDTO(savedReturn));
                response.setTimestamp(new Date());
                return response;
            }

        } else {
            throw new ResponseStatusException(HttpStatus.FORBIDDEN,
                "Role tidak memiliki akses untuk melakukan approval.");
        }

        // Jika sampai di sini berarti disetujui oleh Kepala Departemen SDM
        // Jika disetujui dan perlakuannya Dibuang atau Dijual atau Disumbangkan, kurangi stok cabang
        if (returnObj.getPerlakuan().equals("Dibuang") || 
            returnObj.getPerlakuan().equals("Dijual") || 
            returnObj.getPerlakuan().equals("Disumbangkan")) {
            
            // Ambil data stok cabang
            Optional<StokCabang> stokCabangOptional = stokCabangRepository.findByKodeBarang(returnObj.getKodeBarang());
            if (stokCabangOptional.isPresent()) {
                StokCabang stokCabang = stokCabangOptional.get();
                
                // Pastikan stok cukup
                if (stokCabang.getStokBarang() < returnObj.getStokInput()) {
                    throw new ResponseStatusException(HttpStatus.BAD_REQUEST, 
                        "Stok di cabang tidak mencukupi untuk melakukan return.");
                }
                
                // Kurangi stok cabang
                int stokSebelum = stokCabang.getStokBarang();
                stokCabang.setStokBarang(stokCabang.getStokBarang() - returnObj.getStokInput());
                stokCabangRepository.save(stokCabang);



                logStokBarangService.createLogStokBarang(returnObj.getKodeBarang(),
                        "001", stokSebelum, stokCabang.getStokBarang(), idReturn, "RTRN");

                // Update status retur menjadi SELESAI karena tidak perlu dikembalikan ke pusat
                returnObj.setStatusRetur("SELESAI");
                returnObj.setJumlahDikonfirmasi(returnObj.getStokInput());

                // Kirim notifikasi ke user yang membuat pengajuan
                kirimNotifikasiKeUserPembuat(
                    convertRoleFormat(RolePengguna.KEPALA_DEPARTEMEN_SDM_DAN_UMUM.name()),
                    returnObj.getIdPengajuan(),
                    "Pengajuan return untuk barang " + returnObj.getNamaBarang() +
                    " dengan perlakuan " + returnObj.getPerlakuan() + " telah DISETUJUI dan proses telah SELESAI.",
                    returnObj.getIdInputStokBarangReturn()
                );
            }
        } else if (returnObj.getPerlakuan().equals("Dikembalikan")) {
            // Jika dikembalikan, update status menjadi DIKIRIM
            returnObj.setStatusRetur("DIKIRIM");
            
            // Kurangi stok cabang
            Optional<StokCabang> stokCabangOptional = stokCabangRepository.findByKodeBarang(returnObj.getKodeBarang());
            if (stokCabangOptional.isPresent()) {
                StokCabang stokCabang = stokCabangOptional.get();
                
                // Pastikan stok cukup
                if (stokCabang.getStokBarang() < returnObj.getStokInput()) {
                    throw new ResponseStatusException(HttpStatus.BAD_REQUEST, 
                        "Stok di cabang tidak mencukupi untuk melakukan return.");
                }
                
                // Kurangi stok cabang
                stokCabang.setStokBarang(stokCabang.getStokBarang() - returnObj.getStokInput());
                stokCabangRepository.save(stokCabang);
            }
            
            // Kirim notifikasi ke user yang membuat pengajuan
            kirimNotifikasiKeUserPembuat(
                convertRoleFormat(RolePengguna.KEPALA_DEPARTEMEN_SDM_DAN_UMUM.name()),
                returnObj.getIdPengajuan(),
                "Pengajuan return untuk barang " + returnObj.getNamaBarang() +
                " dengan perlakuan Dikembalikan telah DISETUJUI. Silahkan kirim barang ke gudang pusat.",
                returnObj.getIdInputStokBarangReturn()
            );

            // Kirim notifikasi ke staf gudang bahwa ada barang return yang sedang dikirim
            String nomorCabang = getNomorCabangFromIdPengajuan(returnObj.getIdPengajuan());
            if (nomorCabang == null) {
                nomorCabang = "001";
            }
            notifikasiRestService.kirimNotifikasi(
                convertRoleFormat(RolePengguna.KEPALA_DEPARTEMEN_SDM_DAN_UMUM.name()),
                convertRoleFormat(RolePengguna.STAF_GUDANG_PELAKSANA_UMUM.name()),
                nomorCabang, // Nomor cabang yang benar
                "Ada pengajuan return barang " + returnObj.getNamaBarang() +
                " sebanyak " + returnObj.getStokInput() + " yang akan dikirim ke gudang pusat.",
                returnObj.getIdInputStokBarangReturn()
            );
        }
        
        Return savedReturn = returnRepository.save(returnObj);
        
        response.setStatus(HttpStatus.OK.value());
        response.setMessage("Pengajuan return berhasil disetujui.");
        response.setData(mapToDTO(savedReturn));
        response.setTimestamp(new Date());
        
        return response;
    }
    
    @Override
    @Transactional
    public BaseResponseDTO<ReturnResponseDTO> updateStatusRetur(String idReturn, String newStatus) {
        var response = new BaseResponseDTO<ReturnResponseDTO>();
        
        Optional<Return> returnOptional = returnRepository.findById(idReturn);
        if (returnOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND, 
                "Return dengan ID " + idReturn + " tidak ditemukan.");
        }
        
        Return returnObj = returnOptional.get();
        
        // Update status retur
        returnObj.setStatusRetur(newStatus);
        
        // Jika status menjadi DITERIMA, berarti barang sudah sampai di gudang pusat
        if (newStatus.equals("DITERIMA")) {
            // Dapatkan nomor cabang dari pengajuan
            String nomorCabang = getNomorCabangFromIdPengajuan(returnObj.getIdPengajuan());
            if (nomorCabang == null) {
                nomorCabang = "001";
            }

            // Kirim notifikasi ke staf gudang untuk konfirmasi jumlah
            notifikasiRestService.kirimNotifikasi(
                convertRoleFormat(RolePengguna.KEPALA_OPERASIONAL_CABANG.name()),
                convertRoleFormat(RolePengguna.STAF_GUDANG_PELAKSANA_UMUM.name()),
                nomorCabang, // Nomor cabang yang benar
                "Barang return " + returnObj.getNamaBarang() +
                " sebanyak " + returnObj.getStokInput() + " telah tiba di gudang pusat. " +
                "Mohon konfirmasi jumlah barang yang diterima.",
                returnObj.getIdInputStokBarangReturn()
            );
        }
        
        Return savedReturn = returnRepository.save(returnObj);
        
        response.setStatus(HttpStatus.OK.value());
        response.setMessage("Status return berhasil diupdate menjadi " + newStatus + ".");
        response.setData(mapToDTO(savedReturn));
        response.setTimestamp(new Date());
        
        return response;
    }
    
    @Override
    @Transactional
    public BaseResponseDTO<ReturnResponseDTO> konfirmasiReturn(String idReturn, KonfirmasiReturnRequestDTO konfirmasiDTO) {
        var response = new BaseResponseDTO<ReturnResponseDTO>();

        Optional<Return> returnOptional = returnRepository.findById(idReturn);
        if (returnOptional.isEmpty()) {
            throw new ResponseStatusException(HttpStatus.NOT_FOUND,
                "Return dengan ID " + idReturn + " tidak ditemukan.");
        }

        Return returnObj = returnOptional.get();

        // Pastikan status retur adalah DITERIMA
        if (!returnObj.getStatusRetur().equals("DITERIMA")) {
            throw new ResponseStatusException(HttpStatus.BAD_REQUEST,
                "Return harus berstatus DITERIMA untuk dapat dikonfirmasi.");
        }

        // Update jumlah yang dikonfirmasi
        returnObj.setJumlahDikonfirmasi(konfirmasiDTO.getJumlahDikonfirmasi());

        // Deteksi discrepancy (selisih antara jumlah yang dikirim vs yang dikonfirmasi)
        int jumlahDikirim = returnObj.getStokInput();
        int jumlahDikonfirmasi = konfirmasiDTO.getJumlahDikonfirmasi();
        int selisih = jumlahDikonfirmasi - jumlahDikirim;
        boolean adaDiscrepancy = selisih != 0;

        // Jika perlakuan adalah Dikembalikan, tambahkan ke stok pusat
        if (returnObj.getPerlakuan().equals("Dikembalikan") && konfirmasiDTO.getJumlahDikonfirmasi() > 0) {
            Optional<StokBarang> stokBarangOptional = stokBarangRepository.findById(returnObj.getKodeBarang());
            if (stokBarangOptional.isPresent()) {
                StokBarang stokBarang = stokBarangOptional.get();

                // Tambahkan ke stok pusat
                stokBarang.setStokBarang(stokBarang.getStokBarang() + konfirmasiDTO.getJumlahDikonfirmasi());
                stokBarangRepository.save(stokBarang);
            }
        }

        // Update status menjadi SELESAI
        returnObj.setStatusRetur("SELESAI");

        Return savedReturn = returnRepository.save(returnObj);

        // Dapatkan nomor cabang dari pengajuan
        String nomorCabang = getNomorCabangFromIdPengajuan(returnObj.getIdPengajuan());
        if (nomorCabang == null) {
            nomorCabang = "001";
        }

        // Kirim notifikasi standar bahwa proses return telah selesai
        String pesanStandar = "Proses return barang " + returnObj.getNamaBarang() +
                             " telah selesai. Jumlah yang dikonfirmasi: " + returnObj.getJumlahDikonfirmasi();

        // Kirim notifikasi ke user yang membuat pengajuan
        kirimNotifikasiKeUserPembuat(
            convertRoleFormat(RolePengguna.STAF_GUDANG_PELAKSANA_UMUM.name()),
            returnObj.getIdPengajuan(),
            pesanStandar,
            savedReturn.getIdInputStokBarangReturn()
        );

        // Notifikasi ke staf inventaris dan kepala departemen
        notifikasiRestService.kirimNotifikasi(
            convertRoleFormat(RolePengguna.STAF_GUDANG_PELAKSANA_UMUM.name()),
            convertRoleFormat(RolePengguna.STAF_INVENTARISASI.name()),
            nomorCabang, // Nomor cabang yang benar
            pesanStandar,
            savedReturn.getIdInputStokBarangReturn()
        );

        notifikasiRestService.kirimNotifikasi(
            convertRoleFormat(RolePengguna.STAF_GUDANG_PELAKSANA_UMUM.name()),
            convertRoleFormat(RolePengguna.KEPALA_DEPARTEMEN_SDM_DAN_UMUM.name()),
            nomorCabang, // Nomor cabang yang benar
            pesanStandar,
            savedReturn.getIdInputStokBarangReturn()
        );

        // NOTIFIKASI KHUSUS UNTUK DISCREPANCY
        if (adaDiscrepancy) {
            String pesanDiscrepancy;

            if (selisih > 0) {
                // Kelebihan
                pesanDiscrepancy = "PERHATIAN: Ditemukan KELEBIHAN dalam penerimaan return barang " +
                                  returnObj.getNamaBarang() + ". " +
                                  "Jumlah yang dikirim: " + jumlahDikirim + " unit, " +
                                  "Jumlah yang diterima: " + jumlahDikonfirmasi + " unit. " +
                                  "Kelebihan: " + selisih + " unit. " +
                                  "Mohon dilakukan investigasi untuk mengetahui penyebab kelebihan ini.";
            } else {
                // Kekurangan
                pesanDiscrepancy = "PERHATIAN: Ditemukan KEKURANGAN dalam penerimaan return barang " +
                                  returnObj.getNamaBarang() + ". " +
                                  "Jumlah yang dikirim: " + jumlahDikirim + " unit, " +
                                  "Jumlah yang diterima: " + jumlahDikonfirmasi + " unit. " +
                                  "Kekurangan: " + Math.abs(selisih) + " unit. " +
                                  "Mohon dilakukan investigasi untuk mengetahui penyebab kekurangan ini.";
            }

            // Kirim notifikasi discrepancy ke user yang membuat pengajuan
            kirimNotifikasiKeUserPembuat(
                convertRoleFormat(RolePengguna.STAF_GUDANG_PELAKSANA_UMUM.name()),
                returnObj.getIdPengajuan(),
                pesanDiscrepancy,
                savedReturn.getIdInputStokBarangReturn()
            );

            // Kirim notifikasi discrepancy ke Kepala Departemen SDM dan Umum
            notifikasiRestService.kirimNotifikasi(
                convertRoleFormat(RolePengguna.STAF_GUDANG_PELAKSANA_UMUM.name()),
                convertRoleFormat(RolePengguna.KEPALA_DEPARTEMEN_SDM_DAN_UMUM.name()),
                nomorCabang, // Nomor cabang yang benar
                pesanDiscrepancy,
                savedReturn.getIdInputStokBarangReturn()
            );
        }

        response.setStatus(HttpStatus.OK.value());
        response.setMessage("Konfirmasi return berhasil dilakukan.");
        response.setData(mapToDTO(savedReturn));
        response.setTimestamp(new Date());

        return response;
    }
    
    // Helper method untuk mapping Entity ke DTO
    private ReturnResponseDTO mapToDTO(Return returnObj) {
        return new ReturnResponseDTO(
                returnObj.getIdInputStokBarangReturn(),
                returnObj.getIdPengajuan(),
                returnObj.getKodeBarang(),
                returnObj.getNamaBarang(),
                returnObj.getStokBarangSaatIni(),
                returnObj.getStokInput(),
                returnObj.getPerlakuan(),
                returnObj.getHargaBarang(),
                returnObj.getAlasanReturn(),
                returnObj.getStatusApprovalKepalaCabang(),
                returnObj.getStatusApproval(),
                returnObj.getJumlahDikonfirmasi(),
                returnObj.getStatusRetur()
        );
    }

    // Helper method untuk mendapatkan nomor cabang dari username pengajuan
    private String getNomorCabangFromIdPengajuan(String usernamePengajuan) {
        try {
            // idPengajuan sebenarnya adalah username, bukan idKaryawan
            Pengguna pengguna = penggunaDb.findByUsername(usernamePengajuan);
            if (pengguna != null && pengguna.getNomorCabang() != null) {
                return pengguna.getNomorCabang();
            }

            // Fallback: jika tidak ditemukan, return null untuk menggunakan fallback lain
            return null;
        } catch (Exception e) {
            // Fallback: jika ada error, return null untuk menggunakan fallback lain
            return null;
        }
    }

    // Helper method untuk kirim notifikasi ke user spesifik (Kepala Operasional Cabang yang membuat pengajuan)
    private void kirimNotifikasiKeUserPembuat(String rolePengirim, String usernamePengajuan, String isiNotifikasi, String idReturn) {
        try {
            // idPengajuan sebenarnya adalah username, bukan idKaryawan
            Pengguna userPembuat = penggunaDb.findByUsername(usernamePengajuan);
            if (userPembuat != null && userPembuat.getRole().equals("Kepala Operasional Cabang")) {
                // Kirim notifikasi dengan nomorCabang user yang membuat pengajuan
                notifikasiRestService.kirimNotifikasi(
                    rolePengirim,
                    "Kepala Operasional Cabang",
                    userPembuat.getNomorCabang(),
                    isiNotifikasi,
                    idReturn
                );
            }
        } catch (Exception e) {
            // Jika ada error, fallback ke cara lama
            notifikasiRestService.kirimNotifikasi(
                rolePengirim,
                "Kepala Operasional Cabang",
                "001",
                isiNotifikasi,
                idReturn
            );
        }
    }


    // Tambahkan method helper ini di kelas ReturnServiceImpl
    private String convertRoleFormat(String roleEnum) {
        switch (roleEnum) {
            case "KEPALA_OPERASIONAL_CABANG":
                return "Kepala Operasional Cabang";
            case "KEPALA_DEPARTEMEN_SDM_DAN_UMUM":
                return "Kepala Departemen SDM dan Umum";
            case "STAF_INVENTARISASI":
                return "Staf Inventarisasi";
            case "STAF_GUDANG_PELAKSANA_UMUM":
                return "Staf Gudang Pelaksana Umum";
            default:
                return roleEnum;
        }
    }
}

