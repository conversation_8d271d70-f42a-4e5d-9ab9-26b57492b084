package lblia.propensi.siinven.controller;

import java.time.LocalDate;
import java.util.*;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lblia.propensi.siinven.service.TrenPermintaanBukuService;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.GrafikPermintaanBukuResponseDTO;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.GrafikPermintaanPerBukuResponseDTO;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.NamaBukuResponseDTO;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.PersentasePerubahanPermintaanResponseDTO;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.RataRataPemesananBukuResponseDTO;
import lblia.propensi.siinven.security.jwt.JwtUtils;

@RestController
@RequestMapping("/api/tren-permintaan-buku")
public class TrenPermintaanBukuController {
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    private TrenPermintaanBukuService trenPermintaanBukuService;

    private boolean isInvalidToken(String token) {
        return token == null || !token.startsWith("Bearer ");
    }

    @GetMapping("/permintaan-line-graph/{nomorCabang}/{startDate}/{endDate}")
    public ResponseEntity<?> getLineGraphPermintaan(
            @RequestHeader("Authorization") String token, 
            @PathVariable String nomorCabang,
            @PathVariable LocalDate startDate,
            @PathVariable LocalDate endDate) {
        var baseResponseDTO = new BaseResponseDTO<>();
        
        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Pengadaan dan Pembelian")) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }
        
        List<GrafikPermintaanBukuResponseDTO> listDataGrafik = trenPermintaanBukuService.getGrafikPermintaanBuku(startDate, endDate, nomorCabang);
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(listDataGrafik);
        baseResponseDTO.setMessage("Success");
        return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
    }

    @GetMapping("/permintaan-line-graph/{startDate}/{endDate}")
    public ResponseEntity<?> getLineGraphPermintaanAll(
            @RequestHeader("Authorization") String token,
            @PathVariable LocalDate startDate,
            @PathVariable LocalDate endDate) {
        var baseResponseDTO = new BaseResponseDTO<>();
        
        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Pengadaan dan Pembelian")) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }
        
        List<GrafikPermintaanBukuResponseDTO> listDataGrafik = trenPermintaanBukuService.getGrafikPermintaanBukuAll(startDate, endDate);
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(listDataGrafik);
        baseResponseDTO.setMessage("Success");
        return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
    }

    @GetMapping("/permintaan-line-graph-per-buku/{startDate}/{endDate}")
    public ResponseEntity<?> getLineGraphPermintaanPerBuku(
            @RequestHeader("Authorization") String token,
            @PathVariable LocalDate startDate,
            @PathVariable LocalDate endDate) {
        var baseResponseDTO = new BaseResponseDTO<>();
        
        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Pengadaan dan Pembelian")) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }
        
        List<GrafikPermintaanPerBukuResponseDTO> listDataGrafik = trenPermintaanBukuService.getGrafikPermintaanPerBuku(startDate, endDate);
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(listDataGrafik);
        baseResponseDTO.setMessage("Success");
        return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
    }

    @GetMapping("/rata-rata-pemesanan/{nomorCabang}/{startDate}/{endDate}")
    public ResponseEntity<?> getRataRataPemesanan(
            @RequestHeader("Authorization") String token, 
            @PathVariable String nomorCabang,
            @PathVariable LocalDate startDate,
            @PathVariable LocalDate endDate) {
        var baseResponseDTO = new BaseResponseDTO<>();

        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Pengadaan dan Pembelian")) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }

        List<RataRataPemesananBukuResponseDTO> listDataRataRata = trenPermintaanBukuService.getRataRataPemesanan(startDate, endDate, nomorCabang);
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(listDataRataRata);
        baseResponseDTO.setMessage("Success");
        return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
    }

    @GetMapping("/rata-rata-pemesanan/{startDate}/{endDate}")
    public ResponseEntity<?> getRataRataPemesananAll(
            @RequestHeader("Authorization") String token,
            @PathVariable LocalDate startDate,
            @PathVariable LocalDate endDate) {
        var baseResponseDTO = new BaseResponseDTO<>();

        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Pengadaan dan Pembelian")) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }

        List<RataRataPemesananBukuResponseDTO> listDataRataRata = trenPermintaanBukuService.getRataRataPemesananAll(startDate, endDate);
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(listDataRataRata);
        baseResponseDTO.setMessage("Success");
        return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
    }

    @GetMapping("/persentase-perubahan/{startDate}/{endDate}")
    public ResponseEntity<?> getPersentasePerubahan(
            @RequestHeader("Authorization") String token, 
            @PathVariable LocalDate startDate,
            @PathVariable LocalDate endDate) {
        var baseResponseDTO = new BaseResponseDTO<>();

        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Pengadaan dan Pembelian")) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }

        List<PersentasePerubahanPermintaanResponseDTO> listPersentase = trenPermintaanBukuService.getPersentasePerubahanPermintaan(startDate, endDate);
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(listPersentase);
        baseResponseDTO.setMessage("Success");
        return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
    }


    @GetMapping("/daftar-buku")
    public ResponseEntity<?> getDaftarBuku(@RequestHeader("Authorization") String token){
        var baseResponseDTO = new BaseResponseDTO<>();

        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }

        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Pengadaan dan Pembelian")) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }

        List<NamaBukuResponseDTO> listBuku = trenPermintaanBukuService.getAllNamaBuku();
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(listBuku);
        baseResponseDTO.setMessage("Success");
        return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
    }
}
