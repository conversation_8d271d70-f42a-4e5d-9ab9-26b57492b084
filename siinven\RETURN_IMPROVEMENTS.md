# Perbaikan Fitur CreateReturn

## Ringkasan Perubahan

Fitur createreturn telah diperbaiki sesuai dengan permintaan untuk:

1. **Menampilkan stok barang hanya dari cabang user yang login**
2. **Menambahkan approval bertingkat (Kepala Cabang → Kepala Departemen SDM)**
3. **Menambahkan akses read-only untuk Direktur Utama dan Staf <PERSON>**

## Perubahan Detail

### 1. Model dan Database

**File yang diubah:**
- `siinven/src/main/java/lblia/propensi/siinven/model/Return.java`
- `siinven/src/main/java/lblia/propensi/siinven/dto/response/ReturnResponseDTO.java`

**Perubahan:**
- Menambahkan field `statusApprovalKepalaCabang` untuk tracking approval Kepala Cabang
- Memperbarui constructor dan mapping DTO

**Database Migration:**
- File: `siinven/database_migration_return_approval.sql`
- Menambahkan kolom `status_approval_kepala_cabang` pada tabel `return_stok_barang`

### 2. Controller

**File yang diubah:**
- `siinven/src/main/java/lblia/propensi/siinven/controller/ReturnController.java`

**Perubahan:**
- Menambahkan role `Direktur Utama` dan `Staf keuangan` untuk akses read-only
- Menambahkan role `Kepala Cabang` untuk approval
- Menambahkan endpoint baru `GET /api/return/stock-items` untuk mendapatkan stok barang per cabang
- Memperbarui pesan error untuk mencerminkan approval bertingkat

### 3. Service

**File yang diubah:**
- `siinven/src/main/java/lblia/propensi/siinven/service/ReturnServiceImpl.java`

**Perubahan:**
- Memperbarui `createReturn()` untuk validasi stok barang berdasarkan cabang user
- Memperbarui `approveReturn()` untuk menangani approval bertingkat:
  - Kepala Cabang melakukan approval tahap 1
  - Kepala Departemen SDM melakukan approval tahap 2
- Menambahkan validasi role dan status approval
- Memperbarui `mapToDTO()` untuk field baru

### 4. Dokumentasi

**File yang diubah:**
- `siinven/RETURN.md`

**Perubahan:**
- Memperbarui flow proses return dengan approval bertingkat
- Menambahkan dokumentasi endpoint baru untuk mendapatkan stok barang
- Memperbarui role akses

## Endpoint Baru

### GET /api/return/stock-items

**Deskripsi:** Mendapatkan daftar stok barang yang tersedia di cabang user untuk proses return

**Authorization:** Bearer token (Kepala Operasional Cabang, Staf Inventarisasi)

**Response:** List stok barang yang sama persis dengan dashboard cabang

## Flow Approval Baru

1. **Pengajuan Return** oleh Kepala Operasional Cabang
   - Status: `PENGAJUAN`
   - Status Approval Kepala Cabang: `MENUNGGU`
   - Status Approval Kepala Departemen SDM: `MENUNGGU`

2. **Approval Tahap 1** oleh Kepala Cabang
   - Jika DISETUJUI: lanjut ke tahap 2
   - Jika DITOLAK: status retur menjadi `DITOLAK`

3. **Approval Tahap 2** oleh Kepala Departemen SDM
   - Hanya bisa dilakukan jika sudah disetujui Kepala Cabang
   - Jika DISETUJUI: proses lanjut sesuai perlakuan
   - Jika DITOLAK: status retur menjadi `DITOLAK`

## Role Akses

| Role | Create | Read | Approve | Konfirmasi |
|------|--------|------|---------|------------|
| Kepala Operasional Cabang | ✅ | ✅ | ❌ | ❌ |
| Staf Inventarisasi | ✅ | ✅ | ❌ | ❌ |
| Kepala Cabang | ❌ | ✅ | ✅ (Tahap 1) | ❌ |
| Kepala Departemen SDM | ❌ | ✅ | ✅ (Tahap 2) | ❌ |
| Staf Gudang | ❌ | ✅ | ❌ | ✅ |
| Direktur Utama | ❌ | ✅ | ❌ | ❌ |
| Staf Keuangan | ❌ | ✅ | ❌ | ❌ |

## Cara Testing

1. **Test Endpoint Stok Barang:**
   ```bash
   GET /api/return/stock-items
   Authorization: Bearer [token_kepala_operasional_cabang]
   ```

2. **Test Create Return:**
   ```bash
   POST /api/return
   {
     "kodeBarang": 123,
     "stokInput": 5,
     "perlakuan": "Dikembalikan",
     "alasanReturn": "Barang rusak"
   }
   ```

3. **Test Approval Bertingkat:**
   ```bash
   # Approval oleh Kepala Cabang
   PUT /api/return/{id}/approve
   Authorization: Bearer [token_kepala_cabang]
   {
     "statusApproval": "DISETUJUI"
   }
   
   # Approval oleh Kepala Departemen SDM
   PUT /api/return/{id}/approve
   Authorization: Bearer [token_kepala_departemen_sdm]
   {
     "statusApproval": "DISETUJUI"
   }
   ```

## Catatan Implementasi

- Validasi stok barang sekarang berdasarkan cabang user yang login
- Notifikasi dikirim pada setiap tahap approval
- Database migration diperlukan sebelum deployment
- Semua endpoint existing tetap kompatibel dengan perubahan ini
