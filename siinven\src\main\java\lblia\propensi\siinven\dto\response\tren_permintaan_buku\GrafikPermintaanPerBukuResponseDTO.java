package lblia.propensi.siinven.dto.response.tren_permintaan_buku;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class GrafikPermintaanPerBukuResponseDTO {
    private String monthYear; // Misal "1-2025"
    private Integer totalOrders; // Banyaknya order per bulan
    private String namaBarang; // Represents the book title
    private String nomorCabang; // Represents the branch number
}