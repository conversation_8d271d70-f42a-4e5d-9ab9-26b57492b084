package lblia.propensi.siinven.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.CabangResponseDTO;
import lblia.propensi.siinven.security.jwt.JwtUtils;
import lblia.propensi.siinven.service.Cabang.CabangService;

@RestController
@RequestMapping("/api/cabang")
public class CabangController {
    @Autowired
    private JwtUtils jwtUtils;
    
    @Autowired
    CabangService cabangService;
    
    private boolean isInvalidToken(String token) {
        return token == null || !token.startsWith("Bearer ");
    }

    @GetMapping("/all")
    public ResponseEntity<?> listCabang(@RequestHeader("Authorization") String token) {
        var baseResponseDTO = new BaseResponseDTO<>();
        
        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }
        
        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        if (!(jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Gudang Pelaksana Umum") || 
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Inventarisasi") || 
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Pengadaan dan Pembelian") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Departemen SDM dan Umum") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Direktur Utama") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf keuangan") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Admin") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Operasional Cabang"))) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }

        List<CabangResponseDTO> listCabang = cabangService.listCabang();
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(listCabang);
        baseResponseDTO.setMessage("Success");
        return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
    }

    @GetMapping("/{nomorCabang}")
    public ResponseEntity<?> cabangByNomor(@RequestHeader("Authorization") String token, @PathVariable("nomorCabang") String nomorCabang) {
        var baseResponseDTO = new BaseResponseDTO<>();
        
        if (isInvalidToken(token)) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
        }
        
        if (!jwtUtils.validateJwtToken(token.substring(7))) {
            baseResponseDTO.setStatus(HttpStatus.UNAUTHORIZED.value());
            baseResponseDTO.setMessage("Unauthorized");
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(baseResponseDTO);
        }

        if (!(jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Gudang Pelaksana Umum") || 
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Inventarisasi") || 
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Pengadaan dan Pembelian") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Departemen SDM dan Umum") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Direktur Utama") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf keuangan") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Admin") ||
                jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Operasional Cabang"))) {
            baseResponseDTO.setStatus(HttpStatus.FORBIDDEN.value());
            baseResponseDTO.setMessage("Forbidden");
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body(baseResponseDTO);
        }

        CabangResponseDTO cabang = cabangService.getCabangByNomorCabang(nomorCabang);
        baseResponseDTO.setStatus(HttpStatus.OK.value());
        baseResponseDTO.setData(cabang);
        baseResponseDTO.setMessage("Success");
        return new ResponseEntity<>(baseResponseDTO, HttpStatus.OK);
    }
}
