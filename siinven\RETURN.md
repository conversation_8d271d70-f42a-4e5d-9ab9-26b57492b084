
# Return Barang API

Dokumentasi API untuk pengelolaan proses return barang.

---

## Alur (Flow) Proses Return

1. **Pengajuan Return** (status awal: `PENGAJUAN`)
2. **Approval Tahap 1** oleh Kepala Cabang:
   - <PERSON><PERSON>, lanjut ke tahap 2
   - <PERSON><PERSON> (`DITOLAK`), return tidak dilanjutkan
3. **Approval Tahap 2** oleh Kepala Departemen SDM & Umum:
   - <PERSON><PERSON> (`DISETUJUI`) dan perlakuan = "Dikembalikan", status berubah menjadi `DIKIRIM`
   - <PERSON><PERSON> (`DITOLAK`), return tidak dilanjutkan
4. **Pengiriman Barang**
5. **Penerimaan <PERSON>ang** (ubah status menjadi `DITERIMA`)
6. **Konfirmasi Jumlah Barang** (ubah status menjadi `SELESAI`)

---

## Role Akses

- **Kepala Operasional Cabang:** Mengajukan return
- **Kepala Cabang:** Approval return tahap 1
- **Kepala Departemen SDM & Umum:** Approval return tahap 2
- **Staf Gudang:** Konfirmasi return
- **Direktur Utama:** Melihat list dan detail return (read-only)
- **Staf Keuangan:** Melihat list dan detail return (read-only)

---

## 1. Mendapatkan Stok Barang untuk Return

**Endpoint:** `GET /api/return/stock-items`

**URL:**
`http://localhost:8080/api/return/stock-items`

**Headers:**
- `Authorization: Bearer [token JWT]`

**Deskripsi:**
Endpoint ini mengembalikan daftar stok barang yang tersedia di cabang user untuk proses return. Hanya menampilkan barang yang ada di cabang user yang sedang login.

---

## 2. Membuat Pengajuan Return

**Endpoint:** `POST /api/return`

**URL:**  
`http://localhost:8080/api/return`

**Headers:**
- `Content-Type: application/json`
- `Authorization: Bearer [token JWT]`

**Body Request:**
```json
{
  "kodeBarang": 123,
  "stokInput": 5,
  "perlakuan": "Dikembalikan",
  "alasanReturn": "Barang rusak tidak dapat digunakan"
}
```

---

## 3. Melihat Semua Data Return

**Endpoint:** `GET /api/return`

**URL:**
`http://localhost:8080/api/return`

**Headers:**
- `Authorization: Bearer [token JWT]`

---

## 4. Melihat Return Berdasarkan Status

**Endpoint:** `GET /api/return/status/{status}`

**Contoh URL:**
- Melihat return yang menunggu approval:
  `http://localhost:8080/api/return/status/approval`
- Melihat return dengan status DIKIRIM:
  `http://localhost:8080/api/return/status/DIKIRIM`

**Headers:**
- `Authorization: Bearer [token JWT]`

---

## 5. Melihat Detail Return

**Endpoint:** `GET /api/return/{idReturn}`

**Contoh URL:**
`http://localhost:8080/api/return/RET-001`

**Headers:**
- `Authorization: Bearer [token JWT]`

---

## 6. Approval Return (Bertingkat)

**Endpoint:** `PUT /api/return/{idReturn}/approve`

**URL:**
`http://localhost:8080/api/return/RET-001/approve`

**Headers:**
- `Content-Type: application/json`
- `Authorization: Bearer [token JWT]`

**Deskripsi:**
Approval dilakukan secara bertingkat:
1. **Kepala Cabang** melakukan approval tahap pertama
2. **Kepala Departemen SDM dan Umum** melakukan approval tahap kedua

**Body Request:**
- Jika disetujui:
```json
{
  "statusApproval": "DISETUJUI"
}
```
- Jika ditolak:
```json
{
  "statusApproval": "DITOLAK"
}
```

---

## 7. Update Status Return

**Endpoint:** `PUT /api/return/{idReturn}/status/{newStatus}`

**Contoh URL:**
`http://localhost:8080/api/return/RET-001/status/DITERIMA`

**Headers:**
- `Authorization: Bearer [token JWT]`

---

## 8. Konfirmasi Return

**Endpoint:** `PUT /api/return/{idReturn}/konfirmasi`

**Contoh URL:**  
`http://localhost:8080/api/return/RET-001/konfirmasi`

**Headers:**
- `Content-Type: application/json`
- `Authorization: Bearer [token JWT]`

**Body Request:**
```json
{
  "jumlahDikonfirmasi": 5
}
```

---

## Catatan Penting

- Pastikan **kode barang** sudah **terdaftar** di database sebelum mengajukan return.
- Setiap perubahan status mengikuti flow:
  - `PENGAJUAN` → `DISETUJUI` → `DIKIRIM` → `DITERIMA` → `SELESAI`
  - Jika `DITOLAK`, proses selesai.

- Status-status yang digunakan:
  - `PENGAJUAN`
  - `DISETUJUI`
  - `DITOLAK`
  - `DIKIRIM`
  - `DITERIMA`
  - `SELESAI`
