package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.RequestKomentar;
import lblia.propensi.siinven.dto.response.StokBarangResponseDTO;
import lblia.propensi.siinven.model.StokBarang;

import java.util.HashMap;
import java.util.List;



import lblia.propensi.siinven.dto.response.StokBarangResponseDTO;
import lblia.propensi.siinven.model.StokBarang;

import java.util.List;

public interface StokBarangService {
    List<StokBarangResponseDTO> getAllStokBarang();
    
    StokBarangResponseDTO getStokBarangByKode(Integer kodeBarang);
    
    List<StokBarangResponseDTO> getStokBarangByNomorCabang(String nomorCabang);
    
    List<StokBarangResponseDTO> getStokBarangByKategori(String kategoriBarang);
    
    StokBarangResponseDTO addStokBarang(StokBarang stokBarang);
    

    
    boolean deleteStokBarang(Integer kodeBarang);

    List<HashMap<String, String>> getAllKomentar(String idPengajuan);

    Boolean addKomentar(RequestKomentar body, String role, String username);

    List<StokBarangResponseDTO> getAllStokBarangExceptMainBranch();
    
    

    
    boolean deleteStokBarangByKodeAndNomorCabang(Integer kodeBarang, String nomorCabang);
    
    StokBarangResponseDTO getStokBarangByKodeAndNomorCabang(Integer kodeBarang, String nomorCabang);
    
   boolean existsByKodeBarangAndNomorCabang(Integer kodeBarang, String nomorCabang);

    StokBarangResponseDTO updateStokBarang(StokBarang stokBarang);
}