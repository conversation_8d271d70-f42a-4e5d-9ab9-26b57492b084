package lblia.propensi.siinven.dto.response.CabangKerjaSama;

import java.util.Date;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Data
public class CabangKerjaSamaResponseDTO {
    private String nomorCabang;
    private String namaMitra;
    private String alamat;
    private String kontak;
    private int jumlahKaryawan;
    private String jamOperasional;
    private String masaBerlakuKontrak;
    private String idKaryawanKepalaOperasional;
    private String usernameKepalaOperasionalCabang;
    private String idKaryawanKepalaCabang;
    private String usernameKepalaCabang;
    private String createdAt;
    private String updatedAt;
}
