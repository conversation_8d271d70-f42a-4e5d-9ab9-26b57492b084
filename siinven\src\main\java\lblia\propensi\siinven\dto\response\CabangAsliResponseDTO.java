package lblia.propensi.siinven.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Time;
import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CabangAsliResponseDTO {
    private String nomorCabang;
    private String namaCabang;
    private String alamat;
    private String kontak;
    private Integer jumlahKaryawan;
    private Time jamOperasional;
    private String idKepalaOperasional;
    private String usernameKepalaOperasionalCabang;
    private String idKepalaCabang;
    private String usernameKepalaCabang;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}