package lblia.propensi.siinven.repository;

import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import lblia.propensi.siinven.model.CabangKerjaSama;
import lblia.propensi.siinven.model.Pengguna;

@Repository
public interface CabangKerjaSamaDb extends JpaRepository <CabangKerjaSama, String>{
    CabangKerjaSama findByNomorCabang(String nomorCabang);
    CabangKerjaSama findByKepalaOperasionalCabang(Pengguna kepalaOperasional);
    CabangKerjaSama findByIdKepalaCabang(String kepalaCabang);
    CabangKerjaSama findByNamaMitra(String namaMitra);
}
