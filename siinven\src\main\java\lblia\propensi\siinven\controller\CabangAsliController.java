package lblia.propensi.siinven.controller;

import lblia.propensi.siinven.dto.request.CabangAsliDTO;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.CabangAsliResponseDTO;
import lblia.propensi.siinven.service.CabangAsliRestServiceImpl;
import lblia.propensi.siinven.security.jwt.JwtUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import jakarta.persistence.EntityNotFoundException;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.NoSuchElementException;

@RestController
@RequestMapping("/api/cabang-asli")
public class CabangAsliController {
    
    @Autowired
    private CabangAsliRestServiceImpl cabangAsliService;
    
    @Autowired
    private JwtUtils jwtUtils;

    // Roles allowed to access getAllCabangAsli
    private final List<String> rolesAllowedForGetAll = Arrays.asList(
        "Staf Gudang Pelaksana Umum", 
        "Staf Inventarisasi", 
        "Staf Pengadaan dan Pembelian", 
        "Kepala Departemen SDM dan Umum", 
        "Staf keuangan",
        "Direktur Utama",
        "Admin"
    );

    // Roles allowed to access getCabangAsliById
    private final List<String> rolesAllowedForGetById = Arrays.asList(
        "Kepala Operasional Cabang",
        "Staf Gudang Pelaksana Umum", 
        "Staf Inventarisasi", 
        "Staf Pengadaan dan Pembelian", 
        "Kepala Departemen SDM dan Umum",
        "Staf keuangan",
        "Direktur Utama",
        "Admin",
        "Kepala Cabang"
    );

    @GetMapping("/all")
    // @Operation(summary = "Get all cabang asli", description = "Endpoint untuk mendapatkan semua cabang asli")
    public ResponseEntity<?> getAllCabangAsli(@RequestHeader("Authorization") String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            if (!rolesAllowedForGetAll.contains(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Akses tidak diizinkan");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            List<CabangAsliResponseDTO> cabangAsliList = cabangAsliService.getAllCabangAsli();
            
            BaseResponseDTO<List<CabangAsliResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Berhasil memuat daftar cabang asli");
            response.setTimestamp(new Date());
            response.setData(cabangAsliList);
            
            return ResponseEntity.ok(response);
        } catch (IllegalStateException  e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.CONFLICT.value());
            response.setMessage(e.getMessage());
            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mendapatkan daftar cabang asli: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @GetMapping("/detail/{id}")
    // @Operation(summary = "Get cabang asli by ID", description = "Endpoint untuk mendapatkan detail cabang asli berdasarkan ID")
    public ResponseEntity<?> getCabangAsliById(@PathVariable String id, @RequestHeader("Authorization") String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            if (!rolesAllowedForGetById.contains(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Akses tidak diizinkan");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            CabangAsliResponseDTO cabangAsliResponseDTO = cabangAsliService.getCabangAsliById(id);
            
            BaseResponseDTO<CabangAsliResponseDTO> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Berhasil mendapatkan cabang asli");
            response.setTimestamp(new Date());
            response.setData(cabangAsliResponseDTO);
            
            return ResponseEntity.ok(response);
        } catch (NoSuchElementException e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.NOT_FOUND.value());
            response.setMessage(e.getMessage());
            response.setTimestamp(new Date());
            
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mendapatkan cabang asli: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping
    // @Operation(summary = "Create cabang asli", description = "Endpoint untuk membuat cabang asli baru")
    public ResponseEntity<?> createCabangAsli(@RequestBody CabangAsliDTO cabangAsliDTO, @RequestHeader("Authorization") String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            if (!"Admin".equals(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Hanya Admin yang dapat membuat cabang asli");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            CabangAsliResponseDTO createdCabangAsli = cabangAsliService.createCabangAsli(cabangAsliDTO);
            
            BaseResponseDTO<CabangAsliResponseDTO> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.CREATED.value());
            response.setMessage("Cabang berhasil dibuat");
            response.setTimestamp(new Date());
            response.setData(createdCabangAsli);
            
            return ResponseEntity.status(HttpStatus.CREATED).body(response);
        } catch (EntityNotFoundException e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.NOT_FOUND.value());
            response.setMessage(e.getMessage());
            response.setTimestamp(new Date());
            return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);

        } catch (IllegalArgumentException e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setMessage(e.getMessage());
            response.setTimestamp(new Date());
            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);

        } catch (IllegalStateException e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.CONFLICT.value());
            response.setMessage(e.getMessage());
            response.setTimestamp(new Date());
            return new ResponseEntity<>(response, HttpStatus.CONFLICT);
        
        } catch (NoSuchElementException e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.NOT_FOUND.value());
            response.setMessage(e.getMessage());
            response.setTimestamp(new Date());
            
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setMessage("Gagal membuat cabang asli: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }

    @PutMapping("/update/{id}")
    // @Operation(summary = "Update cabang asli", description = "Endpoint untuk mengupdate cabang asli")
    public ResponseEntity<?> updateCabangAsli(@PathVariable String id, @RequestBody CabangAsliDTO cabangAsliDTO, @RequestHeader("Authorization") String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            if (!"Admin".equals(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Hanya Admin yang dapat mengupdate cabang asli");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            CabangAsliResponseDTO updatedCabangAsli = cabangAsliService.updateCabangAsli(id, cabangAsliDTO);
            
            BaseResponseDTO<CabangAsliResponseDTO> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Berhasil mengupdate cabang asli");
            response.setTimestamp(new Date());
            response.setData(updatedCabangAsli);
            
            return ResponseEntity.ok(response);
        } catch (EntityNotFoundException e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.NOT_FOUND.value());
            response.setMessage(e.getMessage());
            response.setTimestamp(new Date());
            return new ResponseEntity<>(response, HttpStatus.NOT_FOUND);

        } catch (IllegalArgumentException e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setMessage(e.getMessage());
            response.setTimestamp(new Date());
            return new ResponseEntity<>(response, HttpStatus.BAD_REQUEST);

        } catch (IllegalStateException e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.CONFLICT.value());
            response.setMessage(e.getMessage());
            response.setTimestamp(new Date());
            return new ResponseEntity<>(response, HttpStatus.CONFLICT);
        
        } catch (NoSuchElementException e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.NOT_FOUND.value());
            response.setMessage(e.getMessage());
            response.setTimestamp(new Date());
            
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.BAD_REQUEST.value());
            response.setMessage("Gagal mengupdate cabang asli: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
        }
    }

    @DeleteMapping("/delete/{id}")
    // @Operation(summary = "Delete cabang asli", description = "Endpoint untuk menghapus cabang asli")
    public ResponseEntity<?> deleteCabangAsli(@PathVariable String id, @RequestHeader("Authorization") String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }

        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            if (!"Admin".equals(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Hanya Admin yang dapat menghapus cabang asli");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            cabangAsliService.deleteCabangAsli(id);
            
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Berhasil menghapus cabang asli dengan id " + id);
            response.setTimestamp(new Date());
            
            return ResponseEntity.ok(response);
        } catch (NoSuchElementException e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.NOT_FOUND.value());
            response.setMessage(e.getMessage());
            response.setTimestamp(new Date());
            
            return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal menghapus cabang asli: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }


    @PostMapping("/generate-fake/{count}")
    public ResponseEntity<?> generateFakeCabangAsli(@PathVariable int count) {
        cabangAsliService.generateFakeCabangAsli(count);
        
        BaseResponseDTO<String> response = new BaseResponseDTO<>();
        response.setStatus(HttpStatus.OK.value());
        response.setMessage("Berhasil generate " + count + " data fake cabang asli");
        response.setTimestamp(new Date());
        
        return ResponseEntity.ok(response);
    }
}





// package lblia.propensi.siinven.controller;

// import lblia.propensi.siinven.dto.request.CabangAsliDTO;
// import lblia.propensi.siinven.dto.response.BaseResponseDTO;
// import lblia.propensi.siinven.dto.response.CabangAsliResponseDTO;
// import lblia.propensi.siinven.service.CabangAsliRestServiceImpl;
// import lblia.propensi.siinven.security.jwt.JwtUtils;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.HttpStatus;
// import org.springframework.http.ResponseEntity;
// import org.springframework.web.bind.annotation.*;

// import java.util.Date;
// import java.util.List;
// import java.util.NoSuchElementException;

// @RestController
// @RequestMapping("/api/cabang-asli")
// public class CabangAsliController {
    
//     @Autowired
//     private CabangAsliRestServiceImpl cabangAsliService;
    
//     @Autowired
//     private JwtUtils jwtUtils;

//     @GetMapping("/all")
//     public ResponseEntity<?> getAllCabangAsli(@RequestHeader(value = "Authorization", required = false) String token) {
//         try {
//             List<CabangAsliResponseDTO> cabangAsliList = cabangAsliService.getAllCabangAsli();
            
//             BaseResponseDTO<List<CabangAsliResponseDTO>> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.OK.value());
//             response.setMessage("Berhasil memuat daftar cabang asli");
//             response.setTimestamp(new Date());
//             response.setData(cabangAsliList);
            
//             return ResponseEntity.ok(response);
//         } catch (Exception e) {
//             BaseResponseDTO<String> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
//             response.setMessage("Gagal mendapatkan daftar cabang asli: " + e.getMessage());
//             response.setTimestamp(new Date());
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
//         }
//     }

//     @GetMapping("/detail/{id}")
//     public ResponseEntity<?> getCabangAsliById(@PathVariable String id, @RequestHeader(value = "Authorization", required = false) String token) {
//         try {
//             CabangAsliResponseDTO cabangAsliResponseDTO = cabangAsliService.getCabangAsliById(id);
            
//             BaseResponseDTO<CabangAsliResponseDTO> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.OK.value());
//             response.setMessage("Berhasil mendapatkan cabang asli");
//             response.setTimestamp(new Date());
//             response.setData(cabangAsliResponseDTO);
            
//             return ResponseEntity.ok(response);
//         } catch (NoSuchElementException e) {
//             BaseResponseDTO<String> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.NOT_FOUND.value());
//             response.setMessage(e.getMessage());
//             response.setTimestamp(new Date());
            
//             return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
//         } catch (Exception e) {
//             BaseResponseDTO<String> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
//             response.setMessage("Gagal mendapatkan cabang asli: " + e.getMessage());
//             response.setTimestamp(new Date());
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
//         }
//     }

//     @PostMapping
//     public ResponseEntity<?> createCabangAsli(@RequestBody CabangAsliDTO cabangAsliDTO, @RequestHeader(value = "Authorization", required = false) String token) {
//         try {
//             CabangAsliResponseDTO createdCabangAsli = cabangAsliService.createCabangAsli(cabangAsliDTO);
            
//             BaseResponseDTO<CabangAsliResponseDTO> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.CREATED.value());
//             response.setMessage("Cabang berhasil dibuat");
//             response.setTimestamp(new Date());
//             response.setData(createdCabangAsli);
            
//             return ResponseEntity.status(HttpStatus.CREATED).body(response);
//         } catch (Exception e) {
//             BaseResponseDTO<String> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.BAD_REQUEST.value());
//             response.setMessage("Gagal membuat cabang asli: " + e.getMessage());
//             response.setTimestamp(new Date());
//             return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
//         }
//     }

//     @PutMapping("/update/{id}")
//     public ResponseEntity<?> updateCabangAsli(@PathVariable String id, @RequestBody CabangAsliDTO cabangAsliDTO, @RequestHeader(value = "Authorization", required = false) String token) {
//         try {
//             CabangAsliResponseDTO updatedCabangAsli = cabangAsliService.updateCabangAsli(id, cabangAsliDTO);
            
//             BaseResponseDTO<CabangAsliResponseDTO> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.OK.value());
//             response.setMessage("Berhasil mengupdate cabang asli");
//             response.setTimestamp(new Date());
//             response.setData(updatedCabangAsli);
            
//             return ResponseEntity.ok(response);
//         } catch (NoSuchElementException e) {
//             BaseResponseDTO<String> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.NOT_FOUND.value());
//             response.setMessage(e.getMessage());
//             response.setTimestamp(new Date());
            
//             return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
//         } catch (Exception e) {
//             BaseResponseDTO<String> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.BAD_REQUEST.value());
//             response.setMessage("Gagal mengupdate cabang asli: " + e.getMessage());
//             response.setTimestamp(new Date());
//             return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
//         }
//     }

//     @DeleteMapping("/delete/{id}")
//     public ResponseEntity<?> deleteCabangAsli(@PathVariable String id, @RequestHeader(value = "Authorization", required = false) String token) {
//         try {
//             cabangAsliService.deleteCabangAsli(id);
            
//             BaseResponseDTO<String> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.OK.value());
//             response.setMessage("Berhasil menghapus cabang asli dengan id " + id);
//             response.setTimestamp(new Date());
            
//             return ResponseEntity.ok(response);
//         } catch (NoSuchElementException e) {
//             BaseResponseDTO<String> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.NOT_FOUND.value());
//             response.setMessage(e.getMessage());
//             response.setTimestamp(new Date());
            
//             return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
//         } catch (Exception e) {
//             BaseResponseDTO<String> response = new BaseResponseDTO<>();
//             response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
//             response.setMessage("Gagal menghapus cabang asli: " + e.getMessage());
//             response.setTimestamp(new Date());
//             return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
//         }
//     }


//     @PostMapping("/generate-fake/{count}")
//     public ResponseEntity<?> generateFakeCabangAsli(@PathVariable int count) {
//         cabangAsliService.generateFakeCabangAsli(count);
        
//         BaseResponseDTO<String> response = new BaseResponseDTO<>();
//         response.setStatus(HttpStatus.OK.value());
//         response.setMessage("Berhasil generate " + count + " data fake cabang asli");
//         response.setTimestamp(new Date());
        
//         return ResponseEntity.ok(response);
//     }
// }