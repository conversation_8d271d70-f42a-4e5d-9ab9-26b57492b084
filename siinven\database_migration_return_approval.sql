-- Migration script untuk menambahkan kolom approval Kepala Cabang pada tabel return_stok_barang
-- Jalankan script ini untuk memperbarui database sesuai dengan perubahan model Return

-- Menambahkan kolom status_approval_kepala_cabang
ALTER TABLE return_stok_barang 
ADD COLUMN status_approval_kepala_cabang VARCHAR(20) DEFAULT 'MENUNGGU' NOT NULL;

-- Update data existing untuk set default value
UPDATE return_stok_barang 
SET status_approval_kepala_cabang = 'MENUNGGU' 
WHERE status_approval_kepala_cabang IS NULL;

-- Menambahkan comment untuk dokumentasi
COMMENT ON COLUMN return_stok_barang.status_approval_kepala_cabang IS 'Status approval dari Kepala <PERSON>: MENUNGGU, DISETUJUI, DITOLAK';
COMMENT ON COLUMN return_stok_barang.status_approval IS 'Status approval dari Kepala Departemen SDM dan Umum: MENUNGGU, DISETUJUI, DITOLAK';

-- Menambahkan index untuk performa query
CREATE INDEX idx_return_status_approval_kepala_cabang ON return_stok_barang(status_approval_kepala_cabang);
