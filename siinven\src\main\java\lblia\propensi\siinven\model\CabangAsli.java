package lblia.propensi.siinven.model;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.CreationTimestamp;
import org.hibernate.annotations.UpdateTimestamp;

import java.sql.Time;
import java.time.LocalDateTime;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name="cabang_asli")
public class CabangAsli {

    // public enum RolePengguna {
    //     STAF_GUDANG_PELAKSANA_UMUM,
    //     STAF_INVENTARISASI,
    //     STAF_PENGADAAN_DAN_PEMBELIAN,
    //     KEPALA_DEPARTEMEN_SDM_DAN_UMUM,
    //     DIREKTUR_UTAMA,
    //     STAF_KEUANGAN,
    //     ADMIN,
    //     KEPALA_OPERASIONAL_CABANG
    // }

    @Id
    @Column(name = "nomor_cabang", nullable = false)
    private String nomorCabang;

    @Column(name = "nama_cabang", nullable = false)
    private String namaCabang;

    @Column(name = "alamat", nullable = false)
    private String alamat;

    @Column(name = "kontak", nullable = false)
    private String kontak;

    @Column(name = "jumlah_karyawan", nullable = false)
    private Integer jumlahKaryawan;

    @Column(name = "jam_operasional", nullable = false)
    private Time jamOperasional;

    @Column(name = "id_kepala_operasional", nullable = false)
    private String idKepalaOperasional; 

    @Column(name = "id_kepala_cabang", nullable = false)
    private String idKepalaCabang;
    
    @Column(name = "is_cabang_asli", nullable = false, updatable = false)
    private Boolean isCabangAsli = true;

    @CreationTimestamp
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;

    @UpdateTimestamp
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;

    @Column(name = "deleted_at", nullable = true)
    private LocalDateTime deletedAt;

    // One-to-many relationship with Pengguna
    // Each cabang can have many Pengguna (employees)
    @OneToMany(mappedBy = "cabang", fetch = FetchType.LAZY, cascade = CascadeType.ALL)
    private java.util.List<Pengguna> daftarKaryawan;    
}