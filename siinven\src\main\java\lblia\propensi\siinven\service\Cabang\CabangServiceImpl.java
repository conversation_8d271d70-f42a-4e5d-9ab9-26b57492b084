package lblia.propensi.siinven.service.Cabang;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import lblia.propensi.siinven.dto.response.CabangResponseDTO;
import lblia.propensi.siinven.model.CabangAsli;
import lblia.propensi.siinven.model.CabangKerjaSama;
import lblia.propensi.siinven.repository.CabangAsliDb;
import lblia.propensi.siinven.repository.CabangKerjaSamaDb;

@Service
public class CabangServiceImpl implements CabangService {
    @Autowired
    private CabangAsliDb cabangAsliDb;

    @Autowired
    private CabangKerjaSamaDb cabangKerjaSamaDb;

    @Override
    public List<CabangResponseDTO> listCabang() {
        List<CabangAsli> listCabangAsli = cabangAsliDb.findAll();
        List<CabangKerjaSama> listCabangKerjaSama = cabangKerjaSamaDb.findAll();
        List<CabangResponseDTO> listCabang = new ArrayList<>();

        for (CabangAsli cabangAsli: listCabangAsli) {
            CabangResponseDTO cabang = new CabangResponseDTO();
            cabang.setNomorCabang(cabangAsli.getNomorCabang());
            cabang.setNama(cabangAsli.getNamaCabang());
            cabang.setIsCabangAsli(cabangAsli.getIsCabangAsli());
            listCabang.add(cabang);
        }

        for (CabangKerjaSama cabangKerjaSama: listCabangKerjaSama) {
            CabangResponseDTO cabang = new CabangResponseDTO();
            cabang.setNomorCabang(cabangKerjaSama.getNomorCabang());
            cabang.setNama(cabangKerjaSama.getNamaMitra());
            cabang.setIsCabangAsli(cabangKerjaSama.getIsCabangAsli());
            listCabang.add(cabang);
        }
        
        // Sort the listCabang by nomorCabang
        listCabang.sort(Comparator.comparingInt(cabang -> {
            // Convert the nomorCabang to an integer for proper numeric sorting
            return Integer.parseInt(cabang.getNomorCabang());
        }));

        return listCabang;
    }

    @Override
    public CabangResponseDTO getCabangByNomorCabang(String nomorCabang) {
        CabangAsli cabangAsli = cabangAsliDb.findByNomorCabang(nomorCabang);
        CabangKerjaSama cabangKerjaSama = cabangKerjaSamaDb.findByNomorCabang(nomorCabang);
        CabangResponseDTO cabang = new CabangResponseDTO();
        if (cabangAsli != null) {
            cabang.setNomorCabang(cabangAsli.getNomorCabang());
            cabang.setNama(cabangAsli.getNamaCabang());
            cabang.setIsCabangAsli(cabangAsli.getIsCabangAsli());
            cabang.setAlamat(cabangAsli.getAlamat());
        } else {
            cabang.setNomorCabang(cabangKerjaSama.getNomorCabang());
            cabang.setNama(cabangKerjaSama.getNamaMitra());
            cabang.setIsCabangAsli(cabangKerjaSama.getIsCabangAsli());
            cabang.setAlamat(cabangKerjaSama.getAlamat());
        }
        return cabang;
    }
}
