// package lblia.propensi.siinven.controller;

// import jakarta.validation.Valid;
// import lblia.propensi.siinven.dto.request.ApprovalRequestDTO;
// import lblia.propensi.siinven.dto.request.KonfirmasiReturnRequestDTO;
// import lblia.propensi.siinven.dto.request.ReturnRequestDTO;
// import lblia.propensi.siinven.dto.response.BaseResponseDTO;
// import lblia.propensi.siinven.dto.response.ReturnResponseDTO;
// import lblia.propensi.siinven.service.ReturnService;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.http.ResponseEntity;
// import org.springframework.security.access.prepost.PreAuthorize;
// import org.springframework.security.core.Authentication;
// import org.springframework.web.bind.annotation.*;

// import java.util.List;

// @RestController
// @RequestMapping("/api/return")
// public class ReturnController {
    
//     @Autowired
//     private ReturnService returnService;
    
//     @PostMapping
//     public ResponseEntity<BaseResponseDTO<ReturnResponseDTO>> createReturn(
//             @Valid @RequestBody ReturnRequestDTO returnRequestDTO,
//             Authentication authentication) {
//         String idPengaju = authentication.getName(); // Ambil ID user dari token
//         BaseResponseDTO<ReturnResponseDTO> response = returnService.createReturn(returnRequestDTO, idPengaju);
//         return ResponseEntity.status(response.getStatus()).body(response);
//     }
    
//     @GetMapping
//     public ResponseEntity<BaseResponseDTO<List<ReturnResponseDTO>>> getAllReturns() {
//         BaseResponseDTO<List<ReturnResponseDTO>> response = returnService.getAllReturns();
//         return ResponseEntity.status(response.getStatus()).body(response);
//     }
    
//     @GetMapping("/status/{status}")
//     public ResponseEntity<BaseResponseDTO<List<ReturnResponseDTO>>> getReturnsByStatus(
//             @PathVariable String status) {
//         BaseResponseDTO<List<ReturnResponseDTO>> response = returnService.getReturnsByStatus(status);
//         return ResponseEntity.status(response.getStatus()).body(response);
//     }
    
//     @GetMapping("/{idReturn}")
//     public ResponseEntity<BaseResponseDTO<ReturnResponseDTO>> getReturnById(
//             @PathVariable String idReturn) {
//         BaseResponseDTO<ReturnResponseDTO> response = returnService.getReturnById(idReturn);
//         return ResponseEntity.status(response.getStatus()).body(response);
//     }
    
//     @PutMapping("/{idReturn}/approve")
//     public ResponseEntity<BaseResponseDTO<ReturnResponseDTO>> approveReturn(
//             @PathVariable String idReturn,
//             @Valid @RequestBody ApprovalRequestDTO approvalDTO,
//             Authentication authentication) {
//         String idApprover = authentication.getName(); // Ambil ID user dari token
//         BaseResponseDTO<ReturnResponseDTO> response = returnService.approveReturn(idReturn, approvalDTO, idApprover);
//         return ResponseEntity.status(response.getStatus()).body(response);
//     }
    
//     @PutMapping("/{idReturn}/status/{newStatus}")
//     public ResponseEntity<BaseResponseDTO<ReturnResponseDTO>> updateStatusRetur(
//             @PathVariable String idReturn,
//             @PathVariable String newStatus) {
//         BaseResponseDTO<ReturnResponseDTO> response = returnService.updateStatusRetur(idReturn, newStatus);
//         return ResponseEntity.status(response.getStatus()).body(response);
//     }
    
//     @PutMapping("/{idReturn}/konfirmasi")
//     public ResponseEntity<BaseResponseDTO<ReturnResponseDTO>> konfirmasiReturn(
//             @PathVariable String idReturn,
//             @Valid @RequestBody KonfirmasiReturnRequestDTO konfirmasiDTO) {
//         BaseResponseDTO<ReturnResponseDTO> response = returnService.konfirmasiReturn(idReturn, konfirmasiDTO);
//         return ResponseEntity.status(response.getStatus()).body(response);
//     }
// }

package lblia.propensi.siinven.controller;

import jakarta.validation.Valid;
import lblia.propensi.siinven.dto.request.ApprovalRequestDTO;
import lblia.propensi.siinven.dto.request.KonfirmasiReturnRequestDTO;
import lblia.propensi.siinven.dto.request.ReturnRequestDTO;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.ReturnResponseDTO;
import lblia.propensi.siinven.dto.response.StokBarangResponseDTO;
import lblia.propensi.siinven.model.Pengguna;
import lblia.propensi.siinven.security.jwt.JwtUtils;
import lblia.propensi.siinven.service.ReturnService;
import lblia.propensi.siinven.service.StokBarangService;
import lblia.propensi.siinven.service.UserRestService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/api/return")
public class ReturnController {
    
    @Autowired
    private ReturnService returnService;

    @Autowired
    private JwtUtils jwtUtils;

    @Autowired
    private StokBarangService stokBarangService;

    @Autowired
    private UserRestService userRestService;
    
    // Roles allowed to create return request
    private final List<String> rolesAllowedForCreate = Arrays.asList(
        "Kepala Operasional Cabang",
          "Staf Inventarisasi"
    );
    
    // Roles allowed to view all returns
    private final List<String> rolesAllowedForGetAll = Arrays.asList(
        "Kepala Departemen SDM dan Umum",
        "Staf Inventarisasi",
        "Kepala Operasional Cabang",
        "Staf Gudang Pelaksana Umum",
        "Direktur Utama",
        "Staf keuangan"
    );

    // Roles allowed to view returns by status
    private final List<String> rolesAllowedForGetByStatus = Arrays.asList(
        "Kepala Departemen SDM dan Umum",
        "Staf Inventarisasi",
        "Kepala Operasional Cabang",
        "Staf Gudang Pelaksana Umum",
        "Direktur Utama",
        "Staf keuangan"
    );

    // Roles allowed to view return detail
    private final List<String> rolesAllowedForGetById = Arrays.asList(
        "Kepala Departemen SDM dan Umum",
        "Staf Inventarisasi",
        "Kepala Operasional Cabang",
        "Staf Gudang Pelaksana Umum",
        "Direktur Utama",
        "Staf keuangan"
    );

    // Roles allowed to approve return (Kepala Cabang first, then Kepala Departemen SDM)
    private final List<String> rolesAllowedForApprove = Arrays.asList(
        "Kepala Cabang",
        "Kepala Departemen SDM dan Umum"
    );
    
    // Roles allowed to update return status
    private final List<String> rolesAllowedForUpdateStatus = Arrays.asList(
        "Kepala Operasional Cabang",
        "Staf Gudang Pelaksana Umum"
    );
    
    // Roles allowed to confirm return
    private final List<String> rolesAllowedForConfirm = Arrays.asList(
        "Staf Gudang Pelaksana Umum"
    );

    // Roles allowed to get stock items for return
    private final List<String> rolesAllowedForGetStock = Arrays.asList(
        "Kepala Operasional Cabang",
        "Staf Inventarisasi"
    );

    @GetMapping("/stock-items")
    @Operation(summary = "Get stock items for return", description = "Get stock items available for return in user's branch")
    public ResponseEntity<?> getStockItemsForReturn(@RequestHeader("Authorization") String token) {
        try {
            // Validasi token
            if (token == null || !token.startsWith("Bearer ")) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Token tidak valid");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            String jwtToken = token.substring(7);
            if (!jwtUtils.validateJwtToken(jwtToken)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Token tidak terautentikasi");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            String userRole = jwtUtils.getRolesFromJWT(jwtToken);
            String userId = jwtUtils.getUserNameFromJwtToken(jwtToken);

            if (!rolesAllowedForGetStock.contains(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Hanya Kepala Operasional Cabang dan Staf Inventarisasi yang dapat melihat stok barang untuk return");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            // Dapatkan nomor cabang user
            Pengguna pengguna = userRestService.getUserByUsername(userId);
            if (pengguna == null) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.NOT_FOUND.value());
                response.setMessage("User tidak ditemukan");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            String nomorCabang = pengguna.getNomorCabang();
            if (nomorCabang == null) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.BAD_REQUEST.value());
                response.setMessage("Nomor cabang user tidak ditemukan");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            // Dapatkan stok barang berdasarkan cabang
            List<StokBarangResponseDTO> stokBarangList = stokBarangService.getStokBarangByNomorCabang(nomorCabang);

            BaseResponseDTO<List<StokBarangResponseDTO>> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.OK.value());
            response.setMessage("Data stok barang untuk return berhasil ditemukan");
            response.setData(stokBarangList);
            response.setTimestamp(new Date());

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Terjadi kesalahan: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }

    @PostMapping
    public ResponseEntity<?> createReturn(
            @Valid @RequestBody ReturnRequestDTO returnRequestDTO,
            @RequestHeader("Authorization") String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
        
        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            String userId = jwtUtils.getUserNameFromJwtToken(token.substring(7));
            
            if (!rolesAllowedForCreate.contains(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Hanya Kepala Operasional Cabang yang dapat membuat pengajuan return");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            
            BaseResponseDTO<ReturnResponseDTO> response = returnService.createReturn(returnRequestDTO, userId);
            return ResponseEntity.status(response.getStatus()).body(response);
            
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal membuat pengajuan return: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping
    public ResponseEntity<?> getAllReturns(@RequestHeader("Authorization") String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
        
        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            
            if (!rolesAllowedForGetAll.contains(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Akses tidak diizinkan");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            
            BaseResponseDTO<List<ReturnResponseDTO>> response = returnService.getAllReturns();
            return ResponseEntity.status(response.getStatus()).body(response);
            
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mendapatkan data return: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/status/{status}")
    public ResponseEntity<?> getReturnsByStatus(
            @PathVariable String status,
            @RequestHeader("Authorization") String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
        
        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            
            if (!rolesAllowedForGetByStatus.contains(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Akses tidak diizinkan");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            
            BaseResponseDTO<List<ReturnResponseDTO>> response = returnService.getReturnsByStatus(status);
            return ResponseEntity.status(response.getStatus()).body(response);
            
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mendapatkan data return dengan status " + status + ": " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @GetMapping("/{idReturn}")
    public ResponseEntity<?> getReturnById(
            @PathVariable String idReturn,
            @RequestHeader("Authorization") String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
        
        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            
            if (!rolesAllowedForGetById.contains(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Akses tidak diizinkan");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            
            BaseResponseDTO<ReturnResponseDTO> response = returnService.getReturnById(idReturn);
            return ResponseEntity.status(response.getStatus()).body(response);
            
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mendapatkan detail return: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @PutMapping("/{idReturn}/approve")
    public ResponseEntity<?> approveReturn(
            @PathVariable String idReturn,
            @Valid @RequestBody ApprovalRequestDTO approvalDTO,
            @RequestHeader("Authorization") String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
        
        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            String userId = jwtUtils.getUserNameFromJwtToken(token.substring(7));
            
            if (!rolesAllowedForApprove.contains(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Hanya Kepala Cabang dan Kepala Departemen SDM dan Umum yang dapat melakukan approval");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            
            BaseResponseDTO<ReturnResponseDTO> response = returnService.approveReturn(idReturn, approvalDTO, userId);
            return ResponseEntity.status(response.getStatus()).body(response);
            
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal melakukan approval return: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @PutMapping("/{idReturn}/status/{newStatus}")
    public ResponseEntity<?> updateStatusRetur(
            @PathVariable String idReturn,
            @PathVariable String newStatus,
            @RequestHeader("Authorization") String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
        
        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            
            if (!rolesAllowedForUpdateStatus.contains(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Akses tidak diizinkan untuk update status");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            
            // Validasi khusus berdasarkan role dan status yang akan diupdate
            if (userRole.equals("Kepala Operasional Cabang") && !newStatus.equals("DIKIRIM")) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Kepala Operasional Cabang hanya dapat mengubah status menjadi DIKIRIM");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            
            if (userRole.equals("Staf Gudang Pelaksana Umum") && !newStatus.equals("DITERIMA")) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Staf Gudang Pelaksana Umum hanya dapat mengubah status menjadi DITERIMA");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            
            BaseResponseDTO<ReturnResponseDTO> response = returnService.updateStatusRetur(idReturn, newStatus);
            return ResponseEntity.status(response.getStatus()).body(response);
            
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal mengupdate status return: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
    
    @PutMapping("/{idReturn}/konfirmasi")
    public ResponseEntity<?> konfirmasiReturn(
            @PathVariable String idReturn,
            @Valid @RequestBody KonfirmasiReturnRequestDTO konfirmasiDTO,
            @RequestHeader("Authorization") String token) {
        
        if (token == null || !token.startsWith("Bearer ")) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.UNAUTHORIZED.value());
            response.setMessage("Token tidak valid");
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
        }
        
        try {
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.UNAUTHORIZED.value());
                response.setMessage("Unauthorized");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            
            String userRole = jwtUtils.getRolesFromJWT(token.substring(7));
            
            if (!rolesAllowedForConfirm.contains(userRole)) {
                BaseResponseDTO<String> response = new BaseResponseDTO<>();
                response.setStatus(HttpStatus.FORBIDDEN.value());
                response.setMessage("Forbidden: Hanya Staf Gudang Pelaksana Umum yang dapat melakukan konfirmasi");
                response.setTimestamp(new Date());
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            
            BaseResponseDTO<ReturnResponseDTO> response = returnService.konfirmasiReturn(idReturn, konfirmasiDTO);
            return ResponseEntity.status(response.getStatus()).body(response);
            
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.setMessage("Gagal melakukan konfirmasi return: " + e.getMessage());
            response.setTimestamp(new Date());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
        }
    }
}