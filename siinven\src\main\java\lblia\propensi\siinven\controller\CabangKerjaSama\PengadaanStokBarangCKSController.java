package lblia.propensi.siinven.controller.CabangKerjaSama;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lblia.propensi.siinven.dto.request.RequestKomentar;
import lblia.propensi.siinven.dto.request.cabang_asli.InputStokBarangCARequest;
import lblia.propensi.siinven.dto.request.cabang_asli.PersetujuanKepalaCabangStokBarangCabangAsliRequest;
import lblia.propensi.siinven.dto.request.cabang_asli.PersetujuanStokBarangCabangAsliRequest;
import lblia.propensi.siinven.dto.response.BaseResponseDTO;
import lblia.propensi.siinven.dto.response.StokBarangResponseCabangDTO;
import lblia.propensi.siinven.dto.response.cabang_asli.PengadaanCabangAsliTotalResponse;
import lblia.propensi.siinven.model.Pengguna;
import lblia.propensi.siinven.security.jwt.JwtUtils;
import lblia.propensi.siinven.service.PengadaanStokBarangCKSService;
import lblia.propensi.siinven.service.TrenPermintaanBukuService;

import org.springframework.beans.factory.annotation.Autowired;
import lblia.propensi.siinven.service.StokBarangService;
import lblia.propensi.siinven.service.UserRestService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("/api/cabang-kerja-sama")
@Tag(name = "Pengadaan Stok Barang Cabang Kerja Sama", description = "APIs untuk menangani pengadaan stok barang cabang kerja sama")
public class PengadaanStokBarangCKSController {
    private static final Logger logger = LoggerFactory.getLogger(PengadaanStokBarangCKSController.class);
    private final JwtUtils jwtUtils;
    private final PengadaanStokBarangCKSService pengadaanStokBarangCabangAsliService;

    private final UserRestService userRestService;

    @Autowired
    private TrenPermintaanBukuService trenPermintaanBukuService;

    private final StokBarangService stokBarangService;

    public PengadaanStokBarangCKSController(JwtUtils jwtUtils, UserRestService userRestService, PengadaanStokBarangCKSService pengadaanStokBarangCabangAsliService, StokBarangService stokBarangService) {
        this.jwtUtils = jwtUtils;
        this.pengadaanStokBarangCabangAsliService = pengadaanStokBarangCabangAsliService;
        this.stokBarangService = stokBarangService;
        this.userRestService = userRestService;
    }

    @GetMapping("/get-all-pengajuan")
    @Operation(summary = "get all pengajuan", description = "Endpoint untuk mendapatkan semua pengajuan")
    public ResponseEntity<?> getAllPengajuan() {
        try {
            BaseResponseDTO responseDTO = new BaseResponseDTO<>();

            List<HashMap<String, String>> pengajuanList = pengadaanStokBarangCabangAsliService.getAllPengajuan();

            if(pengajuanList.isEmpty()) {
                responseDTO.setStatus(404);
                responseDTO.setMessage("No pengajuan found");
                return ResponseEntity.status(404).body(responseDTO);
            }

            responseDTO.setStatus(200);
            responseDTO.setMessage("Success");
            responseDTO.setData(pengajuanList);

            return new ResponseEntity<>(responseDTO, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(500);
            response.setMessage("Failed to get all pengajuan");
            response.setData(e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @GetMapping("/get-all-pengajuan/{nomorCabang}")
    @Operation(summary = "get all pengajuan by cabang", description = "Endpoint untuk mendapatkan semua pengajuan berdasarkan nomor cabang")
    public ResponseEntity<?> getAllPengajuan(@PathVariable("nomorCabang") String nomorCabang) {
        try {
            BaseResponseDTO responseDTO = new BaseResponseDTO<>();
            List<HashMap<String, String>> pengajuanList = pengadaanStokBarangCabangAsliService.getPengajuanByCabang(nomorCabang);
            if(pengajuanList.isEmpty()) {
                responseDTO.setStatus(404);
                responseDTO.setMessage("No pengajuan found");
                return ResponseEntity.status(404).body(responseDTO);
            }
            responseDTO.setStatus(200);
            responseDTO.setMessage("Success");
            responseDTO.setData(pengajuanList);

            return new ResponseEntity<>(responseDTO, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(500);
            response.setMessage("Failed to get all pengajuan");
            response.setData(e.getMessage());
            return ResponseEntity.status(500).body(response);
        }
    }

    @GetMapping("/get-stock/{nomorCabang}")
    @Operation(summary= "get stock", description = "Endpoint untuk mendapatkan stock")
    public ResponseEntity<?> getStock(@PathVariable("nomorCabang") String nomorCabang) {

        try {
            BaseResponseDTO response = new BaseResponseDTO<>();
            System.out.println("masuk get stock");
            List<StokBarangResponseCabangDTO> copyBarangList = pengadaanStokBarangCabangAsliService.getAllBarang(nomorCabang);

            if (copyBarangList.isEmpty()) {
                response.setStatus(404);
                response.setMessage("No stock found");
                return ResponseEntity.status(HttpStatus.NOT_FOUND).body(response);
            }

            response.setStatus(200);
            response.setMessage("Success");
            response.setData(copyBarangList);

            return new ResponseEntity<>(response, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to get stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @GetMapping("/tabel-pengadaan/{idPengajuan}")
    @Operation(summary= "get tabel pengadaan", description = "Endpoint untuk mendapatkan tabel pengadaan")
    public ResponseEntity<?> getTabelPengadaan(@RequestHeader("Authorization") String token, @PathVariable("idPengajuan") String idPengajuan) {


        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }

            BaseResponseDTO response = new BaseResponseDTO<>();

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                response.setStatus(401);
                response.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            if (!Set.of("Admin", "Staf Gudang Pelaksana Umum", "Kepala Operasional Cabang", "Kepala Departemen SDM dan Umum", "Staf keuangan")
                    .contains(jwtUtils.getRolesFromJWT(token.substring(7)))) {
                response.setStatus(403);
                response.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }
            //request model for adding stock
            PengadaanCabangAsliTotalResponse inputStokBarangList = pengadaanStokBarangCabangAsliService.getInputStokBarangByIdCabang(idPengajuan);

            // create notifikasi

            if (inputStokBarangList == null) {
                response.setStatus(400);
                response.setMessage("No table provided");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            response.setStatus(200);
            response.setMessage("Success");
            response.setData(inputStokBarangList);

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to add stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/departemen-sdm")
    @Operation(summary= "Persetujuan oleh kepala departemen SDM", description = "Endpoint untuk persetujuan pengadaan oleh kepala departemen SDM")
    public ResponseEntity<?> persetujuanKepalaDepartemenSDM(@RequestHeader("Authorization") String token,
                                                            @RequestBody PersetujuanStokBarangCabangAsliRequest pusatRequest) {
        try {

            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }
            BaseResponseDTO response = new BaseResponseDTO<>();

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                response.setStatus(401);
                response.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Departemen SDM dan Umum")) {
                response.setStatus(403);
                response.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            Boolean result = pengadaanStokBarangCabangAsliService.persetujuanInputBarangCabangAsli(pusatRequest, "Kepala Departemen SDM dan Umum");


            if (!result) {
                response.setStatus(400);
                response.setMessage("Failed to approve");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            response.setStatus(200);
            response.setMessage("Success");
            response.setData(result);

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to add stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/staf-keuangan")
    @Operation(summary= "Persetujuan oleh staf keuangan", description = "Endpoint untuk persetujuan pengadaan oleh staf keuangan")
    public ResponseEntity<?> persetujuanStafKeuangan(@RequestHeader("Authorization") String token,  @RequestBody PersetujuanStokBarangCabangAsliRequest pusatRequest) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }

            BaseResponseDTO response = new BaseResponseDTO<>();

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                response.setStatus(401);
                response.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf keuangan")) {
                response.setStatus(403);
                response.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            //request model for agree
            Boolean result =  pengadaanStokBarangCabangAsliService.persetujuanInputBarangCabangAsli(pusatRequest, "Staf keuangan");

            if (result) {
                response.setStatus(200);
                response.setMessage("Success");
                response.setData(true);
            } else {
                response.setStatus(200);
                response.setMessage("Failed to approve");
                response.setData(false);
            }

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to add stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/staf-gudang")
    @Operation(summary= "Persetujuan oleh staf gudang pelaksana umum", description = "Endpoint untuk persetujuan pengadaan oleh staf gudang pelaksana umum")
    public ResponseEntity<?> persetujuanStafGudang(@RequestHeader("Authorization") String token,  @RequestBody PersetujuanStokBarangCabangAsliRequest pusatRequest) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }

            BaseResponseDTO response = new BaseResponseDTO<>();

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                response.setStatus(401);
                response.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Staf Gudang Pelaksana Umum")) {
                response.setStatus(403);
                response.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            //request model for agree
            Boolean result =  pengadaanStokBarangCabangAsliService.persetujuanInputBarangCabangAsli(pusatRequest, "Staf Gudang Pelaksana Umum");

            if (result) {
                response.setStatus(200);
                response.setMessage("Success");
                response.setData(true);
            } else {
                response.setStatus(200);
                response.setMessage("Failed to approve");
                response.setData(false);
            }

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to add stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/kepala-cabang/{nomorCabang}")
    @Operation(summary= "Persetujuan oleh kepala operasional cabang", description = "Endpoint untuk persetujuan pengadaan oleh kepala operasional cabang")
    public ResponseEntity<?> persetujuanKepalaOperasionalCabang(@RequestHeader("Authorization") String token,
                                                                @PathVariable("nomorCabang") String nomorCabang,
                                                                @RequestBody PersetujuanKepalaCabangStokBarangCabangAsliRequest pusatRequest) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }

            BaseResponseDTO response = new BaseResponseDTO<>();

            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                response.setStatus(401);
                response.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }

            if (!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Operasional Cabang")) {
                response.setStatus(403);
                response.setMessage("Forbidden");
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
            }

            //request model for agree
            Boolean result = false;
            if(pusatRequest.getPersetujuan().getStatus()) {
                result =  pengadaanStokBarangCabangAsliService.persetujuanInputBarangCabangAsli(pusatRequest.getPersetujuan(), "Kepala Operasional Cabang");
            } else {
                result = pengadaanStokBarangCabangAsliService.revisiInputStokBarangCabang(pusatRequest.getListInputStokBarang(), pusatRequest.getPersetujuan().getNomorCabang(), pusatRequest.getPersetujuan().getIdPengajuan());
                trenPermintaanBukuService.initializePermintaanBuku(pusatRequest);
            }


            if (result) {
                response.setStatus(200);
                response.setMessage("Success");
                response.setData(true);
            } else {
                response.setStatus(200);
                response.setMessage("Failed to approve");
                response.setData(false);
            }

            return new ResponseEntity<>(response, HttpStatus.OK);

        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to add stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/add-stock")
    @Operation(summary= "add stock", description = "Endpoint untuk menambah stock")
    public ResponseEntity<?> addStock(@RequestHeader("Authorization") String token,
                                      @RequestBody InputStokBarangCARequest listRequest) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }
            BaseResponseDTO response = new BaseResponseDTO<>();
            System.out.println("masuk add stock");
            if (!jwtUtils.validateJwtToken(token.substring(7))) {
                response.setStatus(401);
                response.setMessage("Unauthorized");
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body(response);
            }
            if(!jwtUtils.getRolesFromJWT(token.substring(7)).equals("Kepala Operasional Cabang")) {
                Pengguna pengguna = userRestService.getUserByUsername(jwtUtils.getUserNameFromJwtToken(token.substring(7)));
                if (!pengguna.getNomorCabang().equals(listRequest.getNomorCabang())) {
                    response.setStatus(403);
                    response.setMessage("Forbidden");
                    return ResponseEntity.status(HttpStatus.FORBIDDEN).body(response);
                }
            }
            System.out.println("Authorized");

            PengadaanCabangAsliTotalResponse inputStokBarangList = pengadaanStokBarangCabangAsliService.inputStokBarangCabang(listRequest);
            System.out.println("inputStokBarangList: " + inputStokBarangList);
            if (inputStokBarangList == null) {
                response.setStatus(400);
                response.setMessage("Failed to add stock");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(response);
            }

            response.setStatus(201);
            response.setMessage("Created");
            response.setData(inputStokBarangList);

            return new ResponseEntity<>(response, HttpStatus.CREATED);


        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to add stock");
            return ResponseEntity.status(500).body(response);
        }
    }

    @GetMapping("/keterangan/{idPengajuan}")
    @Operation(summary= "get keterangan", description = "Endpoint untuk mendapatkan keterangan")
    public ResponseEntity<?> getKeterangan(@PathVariable("idPengajuan") String idPengajuan) {
        try {
            BaseResponseDTO responseDTO = new BaseResponseDTO();

            List<HashMap<String, String>> keteranganList = stokBarangService.getAllKomentar(idPengajuan);

            responseDTO.setStatus(200);
            responseDTO.setMessage("Success");
            responseDTO.setData(keteranganList);

            return new ResponseEntity<>(responseDTO, HttpStatus.OK);

        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(400);
            response.setMessage("Failed to get Keterangan");
            return ResponseEntity.status(500).body(response);
        }
    }

    @PostMapping("/keterangan")
    public ResponseEntity<?> addResponse(@RequestHeader("Authorization") String token ,@RequestBody RequestKomentar requestKomentar) {
        try {
            BaseResponseDTO responseDTO = new BaseResponseDTO();
            String rolePengirim = jwtUtils.getRolesFromJWT(token.substring(7));
            String username = jwtUtils.getUserNameFromJwtToken(token.substring(7));
            if (rolePengirim == null) {
                return ResponseEntity.status(HttpStatus.UNAUTHORIZED).body("Invalid token");
            }

            List<String> allowedRoles = List.of("Kepala Departemen SDM dan Umum", "Staf keuangan");

            if (!allowedRoles.contains(rolePengirim)) {
                return ResponseEntity.status(HttpStatus.FORBIDDEN).body("Forbidden");
            }

            if (requestKomentar.getIdPengajuan() == null || requestKomentar.getKeterangan() == null) {
                responseDTO.setStatus(400);
                responseDTO.setMessage("Bad Request");
                return ResponseEntity.status(HttpStatus.BAD_REQUEST).body(responseDTO);
            }

            Boolean result = stokBarangService.addKomentar(requestKomentar, rolePengirim, username);

            responseDTO.setStatus(200);
            responseDTO.setMessage("Success");
            responseDTO.setData(result);
            return new ResponseEntity<>(responseDTO, HttpStatus.OK);
        } catch (Exception e) {
            BaseResponseDTO<String> response = new BaseResponseDTO<>();
            response.setStatus(500);
            response.setMessage("Failed to add keterangan");
            return ResponseEntity.status(500).body(response);
        }
    }
}
