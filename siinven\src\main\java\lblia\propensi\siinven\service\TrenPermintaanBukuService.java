package lblia.propensi.siinven.service;

import java.time.LocalDate;
import java.util.List;

import lblia.propensi.siinven.dto.request.cabang_asli.PersetujuanKepalaCabangStokBarangCabangAsliRequest;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.GrafikPermintaanBukuResponseDTO;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.GrafikPermintaanPerBukuResponseDTO;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.NamaBukuResponseDTO;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.PersentasePerubahanPermintaanResponseDTO;
import lblia.propensi.siinven.dto.response.tren_permintaan_buku.RataRataPemesananBukuResponseDTO;

public interface TrenPermintaanBukuService {
    void initializePermintaanBuku(String nomorCabang, String idPengajuan);

    void initializePermintaanBuku(PersetujuanKepalaCabangStokBarangCabangAsliRequest request);
    
    List<GrafikPermintaanBukuResponseDTO> getGrafikPermintaanBuku(LocalDate startDate, LocalDate endDate, String nomorCabang);
    List<GrafikPermintaanBukuResponseDTO> getGrafikPermintaanBukuAll(LocalDate startDate, LocalDate endDate);
    List<GrafikPermintaanPerBukuResponseDTO> getGrafikPermintaanPerBuku(LocalDate startDate, LocalDate endDate);

    List<RataRataPemesananBukuResponseDTO> getRataRataPemesanan(LocalDate startDate, LocalDate endDate, String nomorCabang);
    List<RataRataPemesananBukuResponseDTO> getRataRataPemesananAll(LocalDate startDate, LocalDate endDate);

    List<PersentasePerubahanPermintaanResponseDTO> getPersentasePerubahanPermintaan(LocalDate startDate, LocalDate endDate);

    List<NamaBukuResponseDTO> getAllNamaBuku();
}
