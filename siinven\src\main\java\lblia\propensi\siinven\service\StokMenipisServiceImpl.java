package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.response.StokMenipisResponseDTO;
import lblia.propensi.siinven.model.Barang;
import lblia.propensi.siinven.model.StokBarang;
import lblia.propensi.siinven.repository.BarangDb;
import lblia.propensi.siinven.repository.StokBarangRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class StokMenipisServiceImpl implements StokMenipisService {

    // Threshold untuk menentukan stok KRITIS dan WARNING
    private static final int KRITIS_THRESHOLD = 5;    // ≤ 5 = KRITIS (merah)
    private static final int WARNING_THRESHOLD = 10;   // 6-10 = WARNING (kuning)

    @Autowired
    private StokBarangRepository stokBarangRepo;

    @Autowired
    private BarangDb barangRepo;

    @Override
    public List<StokMenipisResponseDTO> getAllStokMenipis() {
        return processStokData(
            stokBarangRepo.findByStokBarangLessThanEqual(WARNING_THRESHOLD)
        );
    }

    @Override
    public List<StokMenipisResponseDTO> getStokKritis() {
        return processStokData(
            stokBarangRepo.findByStokBarangLessThanEqual(KRITIS_THRESHOLD)
        );
    }

    @Override
    public List<StokMenipisResponseDTO> getStokWarning() {
        return processStokData(
            stokBarangRepo.findByStokBarangBetween(
                KRITIS_THRESHOLD + 1, 
                WARNING_THRESHOLD
            )
        );
    }

    @Override
    public List<StokMenipisResponseDTO> filterByKategori(String kategori) {
        List<StokBarang> stokMenipis = stokBarangRepo
            .findByStokBarangLessThanEqual(WARNING_THRESHOLD);

        return processStokData(stokMenipis).stream()
            .filter(dto -> dto.getKategoriBarang().equalsIgnoreCase(kategori))
            .collect(Collectors.toList());
    }

    @Override
    public List<String> getAllKategori() {
        return barangRepo.findAllActiveBranches().stream()
            .map(Barang::getKategoriBarang)
            .distinct()
            .collect(Collectors.toList());
    }

    // ===== HELPER METHOD =====
    private List<StokMenipisResponseDTO> processStokData(List<StokBarang> stokList) {
        return stokList.stream()
            .map(stok -> {
                StokMenipisResponseDTO dto = new StokMenipisResponseDTO();
                dto.setKodeBarang(stok.getKodeBarang());
                dto.setNamaBarang(stok.getNamaBarang());
                dto.setStokTerkini(stok.getStokBarang());
                dto.setKategoriBarang(stok.getKategoriBarang());
                dto.setStatus(
                    stok.getStokBarang() <= KRITIS_THRESHOLD ? "KRITIS" : "WARNING"
                );
                return dto;
            })
            .collect(Collectors.toList());
    }
}