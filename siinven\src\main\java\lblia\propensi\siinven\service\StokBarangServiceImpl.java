package lblia.propensi.siinven.service;

import lblia.propensi.siinven.dto.request.RequestKomentar;
import lblia.propensi.siinven.dto.response.StokBarangResponseDTO;
import lblia.propensi.siinven.model.KeteranganPengajuan;
import lblia.propensi.siinven.model.StokBarang;
import lblia.propensi.siinven.repository.KeteranganPengajuanDb;
import lblia.propensi.siinven.repository.StokBarangRepository;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class StokBarangServiceImpl implements StokBarangService {

    @Autowired
    private StokBarangRepository stokBarangRepository;

    @Autowired
    private KeteranganPengajuanDb keteranganPengajuanDb;

    @Override
    public List<StokBarangResponseDTO> getAllStokBarang() {
        return stokBarangRepository.findAll()
                .stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public StokBarangResponseDTO getStokBarangByKode(Integer kodeBarang) {
        Optional<StokBarang> stokBarangOptional = stokBarangRepository.findById(kodeBarang);
        return stokBarangOptional.map(this::mapToDTO).orElse(null);
    }

    @Override
    public List<StokBarangResponseDTO> getStokBarangByNomorCabang(String nomorCabang) {
        List<StokBarang> stokBarangList = getStokBarangByNomor(nomorCabang);
        return stokBarangList.stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    private List<StokBarang> getStokBarangByNomor(String nomorCabang) {
        List<StokBarang> stokBarangList = new ArrayList<>();
        for(StokBarang stokBarang : stokBarangRepository.findAll()) {
            if(stokBarang.getNomorCabang().equals(nomorCabang)) {
                stokBarangList.add(stokBarang);
            }
        }
        return stokBarangList;
    }

    @Override
    public List<StokBarangResponseDTO> getStokBarangByKategori(String kategoriBarang) {
        return stokBarangRepository.findByKategoriBarang(kategoriBarang)
                .stream()
                .map(this::mapToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public StokBarangResponseDTO addStokBarang(StokBarang stokBarang) {
        StokBarang savedStokBarang = stokBarangRepository.save(stokBarang);
        return mapToDTO(savedStokBarang);
    }




    @Override
    public List<HashMap<String, String>> getAllKomentar(String idPengajuan) {
        List<KeteranganPengajuan> keteranganPengajuanList = keteranganPengajuanDb.findAll();
        List<HashMap<String, String>> result = new ArrayList<>();

        for(KeteranganPengajuan keteranganPengajuan: keteranganPengajuanList) {
            if(keteranganPengajuan.getIdPengajuan().equals(idPengajuan)) {
                HashMap<String, String> hashMap = new HashMap<>();
                hashMap.put("waktu_pengajuan", keteranganPengajuan.getWaktuKeterangan().toString());
                hashMap.put("keterangan", keteranganPengajuan.getKeterangan());
                hashMap.put("role_pengirim", keteranganPengajuan.getRolePengirim());
                hashMap.put("username", keteranganPengajuan.getUsername());
                result.add(hashMap);
            }
        }
        return result;
    }

    @Override
    public Boolean addKomentar(RequestKomentar body, String role, String username) {
        try {
            KeteranganPengajuan keteranganPengajuan = new KeteranganPengajuan();
            keteranganPengajuan.setKeterangan(body.getKeterangan());
            keteranganPengajuan.setIdPengajuan(body.getIdPengajuan());
            keteranganPengajuan.setRolePengirim(role);
            keteranganPengajuan.setUsername(username);
            keteranganPengajuan.setWaktuKeterangan(new Date());
            keteranganPengajuanDb.save(keteranganPengajuan);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    private StokBarangResponseDTO mapToDTO(StokBarang stokBarang) {
        return new StokBarangResponseDTO(
                stokBarang.getKodeBarang(),
                stokBarang.getNamaBarang(),
                stokBarang.getKategoriBarang(),
                stokBarang.getHargaBarang(),
                stokBarang.getBentuk(),
                stokBarang.getStokBarang(),
                stokBarang.getNomorCabang()
        );
    }

    @Override
    public List<StokBarangResponseDTO> getAllStokBarangExceptMainBranch() {
    return stokBarangRepository.findAllExceptMainBranch()
            .stream()
            .map(this::mapToDTO)
            .collect(Collectors.toList());
}



    @Override
    public StokBarangResponseDTO getStokBarangByKodeAndNomorCabang(Integer kodeBarang, String nomorCabang) {
        // Option 1: Using iteration
        List<StokBarang> allStokBarang = stokBarangRepository.findAll();
        for (StokBarang stokBarang : allStokBarang) {
            if (stokBarang.getKodeBarang().equals(kodeBarang) && stokBarang.getNomorCabang().equals(nomorCabang)) {
                return mapToDTO(stokBarang);
            }
        }
        
        return null;
        
        // Option 2: If you want to use a custom query instead of iteration (uncommenting this and commenting out the above)
        // List<StokBarang> result = stokBarangRepository.findByKodeBarangAndNomorCabang(kodeBarang, nomorCabang);
        // return result.isEmpty() ? null : mapToDTO(result.get(0));
    }

    @Override
    public boolean deleteStokBarang(Integer kodeBarang) {
        // First find the stock item with the given kodeBarang
        Optional<StokBarang> stokBarangOptional = stokBarangRepository.findById(kodeBarang);
        
        if (stokBarangOptional.isPresent()) {
            // If found, delete it
            stokBarangRepository.deleteById(kodeBarang);
            return true;
        }
        
        return false;
    }

    public boolean deleteStokBarangByKodeAndNomorCabang(Integer kodeBarang, String nomorCabang) {
        // Option 1: Find and then delete
        List<StokBarang> allStokBarang = stokBarangRepository.findAll();
        for (StokBarang stokBarang : allStokBarang) {
            if (stokBarang.getKodeBarang().equals(kodeBarang) && stokBarang.getNomorCabang().equals(nomorCabang)) {
                stokBarangRepository.delete(stokBarang);
                return true;
            }
        }
        
        return false;
        
    // Option 2: If you want to use a custom query (uncommenting this and commenting out the above)
    // List<StokBarang> result = stokBarangRepository.findByKodeBarangAndNomorCabang(kodeBarang, nomorCabang);
    // if (!result.isEmpty()) {
    //     stokBarangRepository.delete(result.get(0));
    //     return true;
    // }
    // return false;
}


@Override
public boolean existsByKodeBarangAndNomorCabang(Integer kodeBarang, String nomorCabang) {
    return stokBarangRepository.existsByKodeBarangAndNomorCabang(kodeBarang, nomorCabang);
}

@Override
public StokBarangResponseDTO updateStokBarang(StokBarang stokBarang) {
    // Find the existing stok barang record
    Optional<StokBarang> existingStokBarangOpt = stokBarangRepository
            .findByKodeBarangAndNomorCabang(stokBarang.getKodeBarang(), stokBarang.getNomorCabang());
    
    if (existingStokBarangOpt.isPresent()) {
        StokBarang existingStokBarang = existingStokBarangOpt.get();
        
        // Update only the fields that need to be updated
        // Assuming StokBarang has these fields - adjust according to your actual model
        existingStokBarang.setStokBarang(stokBarang.getStokBarang());
        // Update other fields as needed
        
        // Save the updated entity (this will update the existing record)
        StokBarang updatedStokBarang = stokBarangRepository.save(existingStokBarang);
        return mapToDTO(updatedStokBarang);
    }
    
    return null;
}
}