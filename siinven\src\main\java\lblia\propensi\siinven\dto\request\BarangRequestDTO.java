package lblia.propensi.siinven.dto.request;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class BarangRequestDTO {
    @NotBlank
    private String namaBarang;

    @NotBlank
    private String kategoriBarang;

    @NotNull
    private Double hargaBarang;

    @NotNull
    @Pattern(regexp = "satuan|paket", message = "Bentuk harus 'satuan' atau 'paket'")
    private String bentuk;
    
    @NotNull
    @Min(value = 0, message = "Jumlah stok minimal harus lebih dari atau sama dengan 0")
    private Integer stokMinimal;
}